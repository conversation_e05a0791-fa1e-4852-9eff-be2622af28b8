// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/material.dart';
// models
import '../models/setting_model.dart';
import '../models/user_model.dart';
// services
import '../services/setting_service.dart';
// tools
import '../tools/app_exception.dart';
import '../tools/app_exception_mapper.dart';

class SettingState extends ChangeNotifier {
  final SettingService _settingService;

  /// ====== 私有状态 ======

  StreamSubscription<SettingModel>? _settingsSubscription;
  String? _currentUserId;
  SettingModel _settings = SettingModel.defaults;
  Locale? _locale;
  AppException? _streamError;

  /// ====== 构造函数 ======

  SettingState(this._settingService) {
    _loadLocale();
  }

  /// ====== 公共状态 ======

  SettingModel get settings => _settings; // 提供完整的 settings 对象
  Locale? get locale => _locale;
  int get weekStartDay => _settings.weekStartDay;
  bool get showCompletedTasks => _settings.showCompletedTasks;
  bool get showOverdueTasks => _settings.showOverdueTasks;
  bool get showDroppedTasks => _settings.showDroppedTasks;
  String? get currentUserId => _currentUserId; // 提供只读的用户ID
  AppException? get streamError => _streamError;

  /// ====== 流监听 ======

  /// 当 UserState 发生变化时，由 ProxyProvider 调用
  /// 这是管理设置数据订阅的唯一入口
  void listenToDependencyChanges(UserModel? user) {
    final oldUserId = _currentUserId;
    final newUserId = user?.id;

    if (oldUserId == newUserId) return;

    _settingsSubscription?.cancel();
    _settingsSubscription = null;
    _currentUserId = newUserId;
    _settings = SettingModel.defaults;
    _streamError = null;
    notifyListeners();

    if (newUserId != null) {
      _listenToSettingChanges(newUserId);
    }
  }

  void _listenToSettingChanges(String userId) {
    if (userId.isEmpty) return;

    _settingsSubscription = _settingService.getSettingStream(userId).listen(
      (settingsFromStream) {
        _settings = settingsFromStream;
        _streamError = null;
        notifyListeners();
      },
      onError: (error, stack) {
        // 无需处理，这是预期的行为
        if (error.code == 'permission-denied') return;

        _streamError = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.loadSetting,
          stack: stack,
        );
        notifyListeners();
      },
    );
  }

  /// ====== 网络请求 ======

  Future<void> setWeekStartDay(int day) async {
    final userId = _currentUserId;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateSetting,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (_settings.weekStartDay == day) return;

    await _settingService.saveWeekStartDay(userId, day);
  }

  Future<void> setShowCompletedTasks(bool value) async {
    final userId = _currentUserId;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateSetting,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (_settings.showCompletedTasks == value) return;

    await _settingService.saveShowCompletedTasks(userId, value);
  }

  Future<void> setShowDroppedTasks(bool value) async {
    final userId = _currentUserId;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateSetting,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (_settings.showDroppedTasks == value) return;

    await _settingService.saveShowDroppedTasks(userId, value);
  }

  Future<void> setShowOverdueTasks(bool value) async {
    final userId = _currentUserId;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateSetting,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (_settings.showOverdueTasks == value) return;

    await _settingService.saveShowOverdueTasks(userId, value);
  }

  // --- 语言设置相关方法 ---

  void _updateLocaleFromCode(String languageCode) {
    if (languageCode == 'system') {
      _locale = null;
    } else {
      _locale = Locale(languageCode);
    }
  }

  Future<void> _loadLocale() async {
    final languageCode = await _settingService.getLocalStorageLanguageCode() ?? 'system';

    _updateLocaleFromCode(languageCode);
    notifyListeners();
  }

  Future<void> setLocale(String languageCode) async {
    await _settingService.setLocalStorageLanguageCode(languageCode);

    _updateLocaleFromCode(languageCode);
    notifyListeners();
  }

  @override
  void dispose() {
    _settingsSubscription?.cancel();
    super.dispose();
  }
}
