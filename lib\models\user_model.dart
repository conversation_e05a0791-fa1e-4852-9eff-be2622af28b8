// import 'dart:developer' as developer;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
// tools
import '../tools/config.dart';
import '../tools/utils.dart';
import '../tools/explicit_value.dart';

/// ⚠️ 这段注释不要删除：
/// ⚠️ 如果 Models 字段有改动
/// ⚠️ 请提醒我同步更新 Cloud Functions 和 Firebase Rules

// authUser 的数据结构
//   - uid
//   - email
//   - phoneNumber
//   - displayName
//   - photoURL
//   - isAnonymous
//   - metadata (creationTime, lastSignInTime)
//   - providerData (List<UserInfo>)
enum UserAuthProvider {
  google,     // 谷歌
  apple,      // 苹果
  email,      // 邮箱密码或邮箱链接
  anonymous,  // 匿名
  unknown;    // 未知或其他

  static bool isGoogleUser(User? authUser) {
    return authUser?.providerData.any((userInfo) => userInfo.providerId == 'google.com') ?? false;
  }

  static bool isAppleUser(User? authUser) {
    return authUser?.providerData.any((userInfo) => userInfo.providerId == 'apple.com') ?? false;
  }

  static UserAuthProvider fromAuth(User authUser) {
    // 先匹配正式账户
    for (final provider in authUser.providerData) {
      switch (provider.providerId) {
        case 'google.com':
          return UserAuthProvider.google;
        case 'apple.com':
          return UserAuthProvider.apple;
        case 'password':
          return UserAuthProvider.email;
      }
    }
    // 匿名账户没有 providerData
    if (authUser.isAnonymous) {
      return UserAuthProvider.anonymous;
    }
    // 测试用户或其他意外情况
    return UserAuthProvider.unknown;
  }
}

class UserModel extends Equatable {
  final String id; // 用户唯一ID (通常来自 Firebase Auth)
  final UserAuthProvider authProvider; // 注册方式
  final String timezone; // 时区
  final DateTime createTime; // 账户创建时间
  final DateTime updateTime; // 账户信息最后更新时间
  final DateTime lastSignInTime; // 最后登录时间（有时登录不需要填账号密码，这也算 Signin）
  final String? email; // 邮箱 (匿名账户没有邮箱)
  final String name; // 显示名称
  final String? photoURL; // 头像 URL
  final UserSubscription subscription; // 付费订阅

  const UserModel({
    required this.id,
    required this.authProvider,
    required this.timezone,
    required this.createTime,
    required this.updateTime,
    required this.lastSignInTime,
    this.email,
    required this.name,
    this.photoURL,
    required this.subscription,
  });

  bool get isAnonymous => authProvider == UserAuthProvider.anonymous;

  /// 创建一个 UserModel 的副本，但用提供的值替换某些字段
  UserModel copyWith({
    ExplicitValue<String>? id,
    ExplicitValue<UserAuthProvider>? authProvider,
    ExplicitValue<String>? timezone,
    ExplicitValue<DateTime>? createTime,
    ExplicitValue<DateTime>? updateTime,
    ExplicitValue<DateTime>? lastSignInTime,
    ExplicitValue<String>? email,
    ExplicitValue<String>? name,
    ExplicitValue<String?>? photoURL,
    ExplicitValue<UserSubscription>? subscription,
  }) {
    return UserModel(
      id: id == null ? this.id : id.value,
      authProvider: authProvider == null ? this.authProvider : authProvider.value,
      timezone: timezone == null ? this.timezone : timezone.value,
      createTime: createTime == null ? this.createTime : createTime.value,
      updateTime: updateTime == null ? this.updateTime : updateTime.value,
      lastSignInTime: lastSignInTime == null ? this.lastSignInTime : lastSignInTime.value,
      email: email == null ? this.email : email.value,
      name: name == null ? this.name : name.value,
      photoURL: photoURL == null ? this.photoURL : photoURL.value,
      subscription: subscription == null ? this.subscription : subscription.value,
    );
  }

  factory UserModel.fromDatabase(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String? ?? '',
      authProvider: Utils.getEnumFromString(UserAuthProvider.values, json['authProvider'] as String?) ?? UserAuthProvider.unknown,
      timezone: json['timezone'] as String? ?? Config.app.defaultTimezone,
      createTime: (json['createTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updateTime: (json['updateTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastSignInTime: (json['lastSignInTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      email: json['email'] as String?, // 可空
      name: json['name'] as String? ?? '',
      photoURL: json['photoURL'] as String?, // 可空
      subscription: UserSubscription.fromDatabase(json['subscription'] as Map<String, dynamic>? ?? {}),
    );
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    id,
    authProvider,
    timezone,
    createTime,
    updateTime,
    lastSignInTime,
    email,
    name,
    photoURL,
    subscription,
  ];
}

////////////////////////////////////////////////
/// 付费订阅
////////////////////////////////////////////////

enum SubscriptionPlan {
  monthly,
  yearly,
  lifetime,
}

enum SubscriptionStatus {
  trial,
  active,
  expired,
  gracePeriod,
}

class UserSubscription extends Equatable {
  final SubscriptionPlan? plan;
  final SubscriptionStatus? status;
  final bool isRenewal;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? paymentSource;
  // 保留的平台特定交易ID
  final String? storeTransactionId;
  final String? latestTransactionId;

  const UserSubscription({
    this.plan,
    this.status,
    this.isRenewal = false,
    this.startTime,
    this.endTime,
    this.paymentSource,
    this.storeTransactionId,
    this.latestTransactionId,
  });

  bool get isPremium => 
      status == SubscriptionStatus.trial ||
      status == SubscriptionStatus.active ||
      status == SubscriptionStatus.gracePeriod;
      
  factory UserSubscription.fromDatabase(Map<String, dynamic> json) {
    return UserSubscription(
      plan: Utils.getEnumFromString(SubscriptionPlan.values, json['plan'] as String?),
      status: Utils.getEnumFromString(SubscriptionStatus.values, json['status'] as String?),
      isRenewal: json['isRenewal'] as bool? ?? false,
      startTime: (json['startTime'] as Timestamp?)?.toDate(),
      endTime: (json['endTime'] as Timestamp?)?.toDate(),
      paymentSource: json['paymentSource'] as String?,
      storeTransactionId: json['storeTransactionId'] as String?,
      latestTransactionId: json['latestTransactionId'] as String?,
    );
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    plan,
    status,
    isRenewal,
    startTime,
    endTime,
    paymentSource,
    storeTransactionId,
    latestTransactionId,
  ];
}
