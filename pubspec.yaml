name: Taskive
description: "a todo app"

publish_to: 'none'

version: 0.3.2+28

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  provider: ^6.1.5
  shared_preferences: ^2.2.3
  firebase_core: ^3.15.0
  cloud_firestore: ^5.6.10
  cloud_functions: ^5.6.0
  firebase_auth: ^5.6.0
  google_sign_in: ^6.3.0
  purchases_flutter: ^8.10.4
  purchases_ui_flutter: ^8.10.4
  table_calendar: ^3.1.3
  fl_chart: ^1.0.0
  lucide_icons_flutter: ^3.0.6
  audioplayers: ^6.5.0
  equatable: ^2.0.7
  app_links: ^6.4.0
  timezone: ^0.10.1
  flutter_timezone: ^4.1.1
  package_info_plus: ^8.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.14.4

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/sounds/
