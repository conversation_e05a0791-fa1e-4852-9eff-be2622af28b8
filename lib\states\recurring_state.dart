// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:collection/collection.dart';
// models
import '../models/recurring_model.dart';
import '../models/user_model.dart';
// services
import '../services/recurring_service.dart';
import '../services/permission_service.dart';
// tools
import '../tools/explicit_value.dart';
import '../tools/app_exception_mapper.dart';
import '../tools/app_exception.dart';

class RecurringState extends ChangeNotifier {
  final RecurringService _recurringService;
  final PermissionService _permissionService;

  /// ====== 私有状态 ======

  StreamSubscription<List<RecurringModel>>? _recurringsSubscription;
  UserModel? _currentUser;
  List<RecurringModel> _recurrings = [];
  AppException? _streamError;

  /// ====== 构造函数 ======

  RecurringState(this._recurringService, this._permissionService);

  /// ====== 公共状态 ======

  // 获取所有周期任务的只读列表
  List<RecurringModel> get recurrings => List.unmodifiable(_recurrings);
  // 获取错误信息
  AppException? get streamError => _streamError;

  /// ====== 流监听 ======

  /// 当 UserState 发生变化时，由 ProxyProvider 调用
  /// 这是管理数据订阅的唯一入口
  void listenToDependencyChanges(UserModel? user) {
    final oldUserId = _currentUser?.id;
    final newUserId = user?.id;

    if (oldUserId == newUserId) return;

    _recurringsSubscription?.cancel();
    _recurringsSubscription = null;
    _currentUser = user;
    _recurrings = [];
    _streamError = null;
    notifyListeners();

    if (newUserId != null) {
      _listenToRecurringChanges(newUserId);
    }
  }

  // 初始化方法：接收 userId 并开始监听周期任务流
  void _listenToRecurringChanges(String userId) {
    if (userId.isEmpty) return;
    
    _recurringsSubscription = _recurringService.getRecurringStream(userId).listen(
      (recurringsFromStream) {
        _recurrings = recurringsFromStream;
        _streamError = null;
        notifyListeners();
      },
      onError: (error, stack) {
        // 无需处理，这是预期的行为
        if (error.code == 'permission-denied') return;

        _streamError = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.loadRecurring,
          stack: stack,
        );
        notifyListeners();
      },
    );
  }

  /// ====== 网络请求 ======

  // 创建周期任务
  Future<RecurringModel?> createRecurring(RecurringModel recurring) async {
    final user = _currentUser;

    if (user == null || user.id.isEmpty) {
      throw AppException(
        how: AppExceptionHow.createRecurring,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    // 使用权限服务执行操作
    final createdRecurringId = await _permissionService.executeWithPermission(
      feature: Feature.recurring,
      currentUsage: _recurrings.length,
      currentUser: user,
      action: () => _recurringService.createRecurring(user.id, recurring),
    );

    if (createdRecurringId != null) {
      return recurring.copyWith(
        id: ExplicitValue(createdRecurringId),
        userId: ExplicitValue(user.id),
      );
    }

    return null;
  }

  // 更新周期任务
  Future<void> updateRecurring(RecurringModel recurring) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateRecurring,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (recurring.userId != userId) {
      throw AppException(
        how: AppExceptionHow.updateRecurring,
        why: AppExceptionWhy.permissionDenied,
      );
    }

    if (recurring.id.isEmpty) return;

    // 调用服务更新, 不捕获异常，由 UI 层处理
    await _recurringService.updateRecurring(userId, recurring);
  }

  // 删除周期任务
  Future<void> deleteRecurring(String recurringId) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.deleteRecurring,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (recurringId.isEmpty) return;

    // 调用服务删除, 不捕获异常，由 UI 层处理
    await _recurringService.deleteRecurring(recurringId);
  }

  /// ====== 工具方法 ======
  
  /// 检查用户是否可以创建新的周期任务 (用于 UI)
  bool canCreateRecurring() {
    final user = _currentUser;
    if (user == null) return false;
    return _permissionService.canPerformAction(
      Feature.recurring,
      _recurrings.length,
      user,
    );
  }

  // 根据 ID 获取周期任务
  RecurringModel? getRecurringById(String recurringId) {
    return _recurrings.firstWhereOrNull((recurring) => recurring.id == recurringId);
  }

  // 清理资源：取消所有流订阅
  @override
  void dispose() {
    _recurringsSubscription?.cancel();
    super.dispose();
  }
}
