// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/timer_model.dart';
// states
import '../../states/task_state.dart';
import '../../states/project_state.dart';
// tools
import '../../tools/config.dart';
import '../../tools/extensions.dart';
// sheets
import '../../sheets/timer/timer_edit_sheet.dart';
// pages
import '../../pages/timer/task_timers_page.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TimerCard extends StatelessWidget {
  final TimerModel timer;

  const TimerCard({
    super.key,
    required this.timer,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    final taskState = Provider.of<TaskState>(context);
    final projectState = Provider.of<ProjectState>(context);
    final task = taskState.getTaskById(timer.taskId);
    // 处理 task 可能为 null 的情况
    final taskName = task?.name ?? l10n.common_unknownTask;
    final project = projectState.getProjectById(task?.projectId ?? '');

    // 生成时长显示
    final durationText = timer.durationSeconds.format();

    // 定义图标尺寸
    const double iconSize = 28.0;

    return Card(
      // 样式与大小尽量与 TaskCard 一致
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 3.0),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 左侧图标区域
            SizedBox(
              width: iconSize + 15,
              height: iconSize + 10,
              child: IconButton(
                icon: Icon(
                  LucideIcons.circleDot,
                  color: project?.color ?? colorScheme.onSurfaceVariant,
                  size: iconSize,
                ),
                padding: const EdgeInsets.only(left: 5.0, right: 10.0),
                splashRadius: iconSize * 0.8,
                constraints: const BoxConstraints(),
                alignment: Alignment.center,
                onPressed: () {
                  HapticFeedback.lightImpact();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TaskTimersPage(taskId: timer.taskId),
                    ),
                  );
                },
              ),
            ),

            // 右侧信息区域
            Expanded(
              child: InkWell(
                onTap: () {
                  HapticFeedback.lightImpact();
                  // 点击图标时显示编辑弹窗
                  TimerEditSheet.show(context, timer: timer);
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 第一部分：任务名
                      Text(
                        taskName,
                        style: const TextStyle(fontSize: 15.0),
                        maxLines: Config.app.taskNameMaxLines,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6.0),
                      // 第二部分：元数据
                      Row(
                        children: [
                          // 左侧：时间区间信息
                          Text(
                            _formatDateTime(l10n),
                            style: TextStyle(
                              fontSize: 14.0,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const Spacer(),
                          // 右侧：时长信息
                          Text(
                            durationText,
                            style: TextStyle(
                              fontSize: 16.0,
                              color: colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 右侧 padding
            const SizedBox(width: 6.0),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(AppLocalizations l10n) {
    final startTime = timer.startTime;

    final dateText = startTime.formatDate();
    final weekdayText = startTime.formatWeekday(l10n);
    final timeSpanText = startTime.formatSpan(timer.endTime);

    return '$dateText $weekdayText $timeSpanText';
  }
}
