// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// models
import '../../models/task_model.dart';
// states
import '../../states/project_state.dart';
import '../../states/task_state.dart';
import '../../states/timer_state.dart';
// tools
import '../../tools/extensions.dart';
import '../../tools/explicit_value.dart';
import '../../tools/bottom_slide_route.dart';
import '../../tools/config.dart';
// widgets
import './task_checkbox.dart';
import '../project/project_badge.dart';
import '../common/base_label.dart';
// sheets
import '../../sheets/task/task_longpress_sheet.dart';
import '../../sheets/task/task_edit_page.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskCard extends StatelessWidget {
  final TaskModel task;
  final VoidCallback? onTap;

  const TaskCard({
    super.key,
    required this.task,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final taskState = Provider.of<TaskState>(context);
    final projectState = Provider.of<ProjectState>(context);
    TimerNotifier timerNotifier = Provider.of<TimerNotifier>(context);

    final project = projectState.getProjectById(task.projectId ?? '');
    final hasRunningTimer = timerNotifier.state.isThisTaskHasRunningTimer(task.id);
    final timerCount = timerNotifier.state.getTimerCountForTask(task.id);
    final durationText = timerNotifier.state.getTotalSecondsForTask(task.id).formatWithUnit();

    // 使用 GestureDetector 包装 Card 以处理长按事件
    return GestureDetector(
      onLongPress: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();
        // 调用显示底部操作菜单的函数
        TaskLongPressSheet.show(context, task);
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 3.0),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧图标区域
              TaskCheckbox(
                task: task,
                hasRunningTimer: hasRunningTimer,
                onChanged: (bool? value) {
                  if (value != null) {
                    final updatedTask = task.copyWith(
                      completeTime: ExplicitValue(value ? DateTime.now() : null),
                    );
                    taskState.updateTask(updatedTask);
                  }
                },
              ),

              // 右侧信息区域
              Expanded(
                child: InkWell(
                  // onTap 仍然用于编辑任务，但仅在没有运行计时器时
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    if (!hasRunningTimer) {
                      Navigator.of(context).push(BottomSlideRoute(page: TaskEditPage(task: task)));
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 第一部分：任务名
                        Text(
                          task.name,
                          style: TextStyle(
                            decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                            color: (task.isCompleted || hasRunningTimer)
                              ? Theme.of(context).colorScheme.onSurfaceVariant
                              : Theme.of(context).colorScheme.onSurface,
                            fontSize: 15.0,
                            // fontWeight: task.isCompleted ? FontWeight.normal : FontWeight.w500,
                          ),
                          maxLines: Config.app.taskNameMaxLines,
                          overflow: TextOverflow.ellipsis,
                        ),

                        // 第二部分：元数据
                        if (!task.hasNoDue || timerCount > 0 || project != null) ...[
                          const SizedBox(height: 6.0),
                          Row(
                            children: [
                              // 左侧：日期信息
                              if (!task.hasNoDue)
                                Text(
                                  _formatDateTime(l10n),
                                  style: TextStyle(
                                    fontSize: 14.0,
                                    color: task.isOverdue
                                      ? Theme.of(context).colorScheme.error
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              
                              // 左侧：计时信息
                              if (timerCount > 0)
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (!task.hasNoDue)
                                      const SizedBox(width: 8.0),
                                    BaseLabel(
                                      text: '${timerCount}C',
                                      color: Theme.of(context).colorScheme.secondary,
                                      fontSize: 11.0,
                                    ),
                                    const SizedBox(width: 4.0),
                                    BaseLabel(
                                      text: durationText,
                                      color: Theme.of(context).colorScheme.primary,
                                      fontSize: 11.0,
                                    ),
                                  ],
                                ),
                              
                              // 右侧：项目信息
                              if (project != null) ...[
                                const SizedBox(width: 8.0),
                                Expanded(
                                  child: Align(
                                    alignment: Alignment.centerRight,
                                    child: ProjectBadge(project: project),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              // 右侧padding
              const SizedBox(width: 6.0),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDateTime(AppLocalizations l10n) {
    final deadline = task.deadlineTime;
    String datetimeText = '';

    // 格式化日期部分
    if (deadline != null) {
      datetimeText = deadline.formatDate();

      // 如果是精确时刻任务，添加时间显示 (24小时制)
      if (task.hasDueDateClock) {
        datetimeText += ' ${deadline.formatClock()}';
      }

      // 添加星期
      datetimeText += ' ${deadline.formatWeekday(l10n)}';
    }
    
    return datetimeText;
  }
}
