import { getAuth } from 'firebase-admin/auth';
import { FieldValue } from 'firebase-admin/firestore';
import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { db, FIREBASE, DATABASE, INPUT, AUTH_PROVIDERS } from '../tools/config.js';
import { createDemoDataForNewUser } from './demo.js';


// 辅助函数：根据用户的 providerData 推断其主要的认证方式
function _getAuthProvider(user: { providerData: { providerId: string }[] }): string {
  // 本来可以通过 user.isAnonymous 获得
  // 但 nodejs v1 auth user 没有这个属性
  // 列表为空时说明是匿名账户
  if (user.providerData.length === 0) {
    return AUTH_PROVIDERS.find(p => p.isAnonymous)!.name;
  }

  // 获取用户拥有的所有 providerId
  const userProviderIds = new Set(user.providerData.map(p => p.providerId));

  // 按照我们定义的优先级，查找用户匹配的第一个提供商
  for (const provider of AUTH_PROVIDERS) {
    if (userProviderIds.has(provider.id)) return provider.name;
  }
  
  // 返回未知的认证方式作为兜底
  return AUTH_PROVIDERS.find(p => p.isUnknown)!.name;
}


/**
 * 创建账户
 * 负责在 Firestore 中创建对应的用户文档，并补全必要信息
 */
export const createAccount = onCall({ region: FIREBASE.REGION }, async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
  }

  const uid = request.auth.uid;
  const { timezone, withDemo } = request.data;

  if (typeof timezone !== 'string' || timezone.length === 0 || typeof withDemo !== 'boolean') {
    throw new HttpsError('invalid-argument', 'A valid timezone string and withDemo boolean must be provided.');
  }

  try {
    const userDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid);

    const docSnap = await userDocRef.get();
    if (docSnap.exists) {
      logger.warn(`用户 ${uid} 的 Firestore 文档已存在，跳过创建`);
      return;
    }

    const authUser = await getAuth().getUser(uid);

    // 设定默认用户名
    const userName = authUser.displayName || `user_${uid.substring(0, 6)}`;

    // 如果原始 displayName 为空，则需要同步到 Auth 服务
    if (!authUser.displayName) {
      await getAuth().updateUser(uid, { displayName: userName });
    }

    // 创建新用户数据
    const newUser = {
      authProvider: _getAuthProvider(authUser),
      timezone: timezone,
      createTime: FieldValue.serverTimestamp(),
      updateTime: FieldValue.serverTimestamp(),
      lastSignInTime: FieldValue.serverTimestamp(),
      email: authUser.email || null,
      name: userName,
      photoURL: authUser.photoURL || null,
      subscription: {}, // 初始订阅状态为空对象
    };

    // 在 Firestore 中创建用户文档
    await userDocRef.set(newUser);

    if (withDemo) {
      // 为新用户创建引导数据
      await createDemoDataForNewUser(uid, userDocRef, timezone);
    }

    logger.info(`成功为新用户 ${uid} 创建了 Firestore 文档`);

    return { success: true };
  } catch (error) {
    logger.error(`为新用户 ${uid} 创建 Firestore 文档时失败`, error);
    if (error instanceof HttpsError) throw error;
    throw new HttpsError('internal', 'An unknown error occurred while creating account', error);
  }
});


/**
 * 记录用户登录事件
 * 这是一个轻量级的函数，只负责更新 lastSignInTime 字段
 * 在每次用户会话被验证时调用（如 App 打开）
 */
export const recordSignIn = onCall({ region: FIREBASE.REGION }, async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
  }

  const uid = request.auth.uid;
  const timezone = request.data?.timezone;

  // if (typeof timezone !== 'string' || timezone.length === 0) {
  //   throw new HttpsError('invalid-argument', 'A valid timezone string must be provided.');
  // }

  try {
    const userDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid);

    await userDocRef.update({
      timezone: timezone ?? DATABASE.DEFAULT_TIMEZONE,
      lastSignInTime: FieldValue.serverTimestamp(),
    });

    return { success: true };

  } catch (error) {
    // 如果文档不存在（极小概率），记录错误但不要让客户端失败
    logger.error(`为用户 ${uid} 记录登录时间时发生错误`, error);
    // 我们可以返回成功，因为这是一个非关键操作
    return { success: true, message: 'User document might not exist yet' };
  }
});


/**
 * 匿名账户升级为正式账户
 * 这是一个重量级的函数，负责将 Auth 信息同步到 Firestore 和 Auth 顶层
 * 它也包含了更新登录时间的职责
 */
export const upgradeAccount = onCall({ region: FIREBASE.REGION }, async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
  }

  const uid = request.auth.uid;
  const timezone = request.data?.timezone;

  // if (typeof timezone !== 'string' || timezone.length === 0) {
  //   throw new HttpsError('invalid-argument', 'A valid timezone string must be provided');
  // }

  try {
    const userDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid);

    // 并行获取 Auth 和 Firestore 的用户信息
    const [authUser, userDocSnap] = await Promise.all([
      getAuth().getUser(uid),
      userDocRef.get()
    ]);

    if (!userDocSnap.exists) {
      // 这种情况理论上不应该发生，因为 onUserCreate 触发器应该已经创建了文档。
      // 但作为防御性编程，我们记录一个警告。
      logger.error(`upgradeAccount 被调用，但用户 ${uid} 的 Firestore 文档不存在，将跳过更新`);
      // 虽然理论上不应发生，但如果发生，这是一个明确的服务端状态问题。
      throw new HttpsError('not-found', `User document for UID ${uid} not found`);
    }

    const storedUserData = userDocSnap.data() || {};
    const firestoreData: { [key: string]: any } = {};
    const authData: { displayName?: string; photoURL?: string } = {};

    // 升级 oauth 账户后，authUser.providerData 的数据会即时更新，而 authUser 的数据不会
    // 因此通过 authUser.providerData 来获取 oauth 登录后的用户名和头像
    const oauthProvider = authUser.providerData.find(userInfo => 
      AUTH_PROVIDERS.some(config => config.id === userInfo.providerId && config.isOauth)
    );

    // 1. 同步认证方式
    const newAuthProvider = _getAuthProvider(authUser);
    if (storedUserData.authProvider !== newAuthProvider) {
      firestoreData.authProvider = newAuthProvider;
    }

    // 2. 同步邮箱 (例如从匿名状态转为正式用户)
    if (authUser.email && storedUserData.email !== authUser.email) {
      firestoreData.email = authUser.email;
    }

    // 3. 同步用户名 (例如用户通过 Google/Apple 首次登录)
    const oauthName = oauthProvider?.displayName;
    if (oauthName && storedUserData.name !== oauthName) {
      firestoreData.name = oauthName;
      authData.displayName = oauthName;
    }

    // 4. 同步头像 (例如用户通过 Google/Apple 首次登录)
    const oauthPhotoURL = oauthProvider?.photoURL;
    if (oauthPhotoURL && storedUserData.photoURL !== oauthPhotoURL) {
      firestoreData.photoURL = oauthPhotoURL;
      authData.photoURL = oauthPhotoURL;
    }

    if (Object.keys(firestoreData).length > 0) {
      // 更新了其他用户数据，需要更新 updateTime
      firestoreData.timezone = timezone ?? DATABASE.DEFAULT_TIMEZONE;
      firestoreData.updateTime = FieldValue.serverTimestamp();
      firestoreData.lastSignInTime = FieldValue.serverTimestamp();
    } else {
      firestoreData.timezone = timezone ?? storedUserData.timezone;
      firestoreData.lastSignInTime = FieldValue.serverTimestamp();
    }

    // 一定需要更新 firestore，所以直接 push 进去
    const updatePromises: Promise<any>[] = [userDocRef.update(firestoreData)];

    if (Object.keys(authData).length > 0) {
      // 需要更新 auth 才 push 进去
      updatePromises.push(getAuth().updateUser(uid, authData));
    }

    // 并行更新 Firestore 和 Firebase Auth (如果两个都需要更新数据)
    await Promise.all(updatePromises);
    
    return { success: true };
  } catch (error) {
    logger.error(`为用户 ${uid} 升级账户时发生错误`, error);
    // 检查是否已经是 HttpsError，如果是，则直接抛出
    if (error instanceof HttpsError) throw error;
    throw new HttpsError('internal', 'An unknown error occurred while upgrading account', error);
  }
});


/**
 * 更新用户的用户名
 * 这是一个可从客户端调用的函数
 * @param {{ newName: string }} data 包含新用户名的对象
 */
export const updateUsername = onCall({ region: FIREBASE.REGION }, async (request) => {
  // 1. 身份验证检查
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
  }

  // 2. 数据验证
  const newName = request.data.newName;

  if (typeof newName !== 'string' || newName.trim().length === 0 || newName.trim().length > INPUT.USERNAME_MAX_LENGTH) {
    throw new HttpsError(
      'invalid-argument',
      `Username cannot be empty and must be no more than ${INPUT.USERNAME_MAX_LENGTH} characters long`,
    );
  }

  const uid = request.auth.uid;
  const userDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid);

  try {
    const trimmedName = newName.trim();
    // 3. 并行更新 Firestore 和 Firebase Auth，确保数据一致性
    await Promise.all([
      // 更新 Firestore
      userDocRef.update({
        name: trimmedName,
        updateTime: FieldValue.serverTimestamp(),
      }),
      // 更新 Firebase Auth
      getAuth().updateUser(uid, { displayName: trimmedName }),
    ]);

    return { success: true, message: `Username for user ${uid} has been updated to ${trimmedName}.` };
  } catch (error) {
    logger.error(`更新用户 ${uid} 的用户名时发生错误`, error);
    // 检查是否已经是 HttpsError，如果是，则直接抛出
    if (error instanceof HttpsError) throw error;
    throw new HttpsError('internal', 'An unknown error occurred while updating the username', error);
  }
});


/**
 * 删除一个用户账户及其在 Firestore 中的所有关联数据。
 * 这是一个可从客户端调用的、高风险的操作。
 */
export const deleteAccount = onCall({ region: FIREBASE.REGION }, async (request) => {
  // 1. 身份验证检查
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
  }

  const uid = request.auth.uid;

  try {
    // 2. 从 Firestore 删除所有用户相关数据 (包括所有子集合)
    await deleteUserFirestoreData(uid);

    // 3. 从 Firebase Authentication 中永久删除用户
    await getAuth().deleteUser(uid);

    return { success: true, message: `All data for user ${uid} has been successfully deleted` };
  } catch (error: any) {
    // 4. 精确的错误处理
    logger.error(`删除用户 ${uid} 时发生严重错误`, error);

    // 如果是 Auth 的 "user-not-found" 错误
    if (error.code === 'auth/user-not-found') {
      throw new HttpsError('not-found', 'The user to be deleted was not found');
    }

    // 如果是其他我们已经包装好的 HttpsError
    if (error instanceof HttpsError) throw error;

    // 对于所有其他未知错误（例如 deleteUserFirestoreData 失败）
    throw new HttpsError('internal', 'An unknown error occurred while deleting the account', error);
  }
});


/**
 * 协调并执行删除用户在 Firestore 中的全部数据的操作
 * @param {string} uid 用户的 UID
 */
async function deleteUserFirestoreData(uid: string) {
  const userDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid);

  // 需要为用户删除的子集合列表
  const subCollections = [
    DATABASE.TASKS_COLLECTION,
    DATABASE.TIMERS_COLLECTION,
    DATABASE.PROJECTS_COLLECTION,
    DATABASE.RECURRINGS_COLLECTION,
    DATABASE.SETTINGS_COLLECTION,
  ];

  for (const collectionName of subCollections) {
    const collectionRef = userDocRef.collection(collectionName);
    await deleteCollection(collectionRef);
  }

  // 在所有子集合都被清空后，删除用户的主文档
  await userDocRef.delete();
}


/**
 * 以指定批次大小，递归删除一个集合或子集合中的所有文档
 * 这个函数会持续执行，直到集合中的所有文档都被删除
 * @param {FirebaseFirestore.CollectionReference} collectionRef 要删除的集合的引用
 */
async function deleteCollection(collectionRef: FirebaseFirestore.CollectionReference): Promise<void> {
  const query = collectionRef.orderBy('__name__').limit(DATABASE.BATCH_LIMIT);

  while (true) {
    const snapshot = await query.get();

    // 当查询结果为空时，说明所有文档都已删除，完成操作
    if (snapshot.size === 0) {
      return;
    }

    // 使用批量写入来删除当前批次的文档
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    await batch.commit();

    // 为了防止潜在的内存或速率限制问题，在批次之间添加一个短暂的延迟
    await new Promise(resolve => setTimeout(resolve, 50));
  }
}
