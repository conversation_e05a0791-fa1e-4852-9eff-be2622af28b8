{"@@locale": "fr", "common_today": "<PERSON><PERSON><PERSON>'hui", "common_tomorrow": "<PERSON><PERSON><PERSON>", "common_yesterday": "<PERSON>er", "common_daysAgo": "Il y a {count} jours", "common_daysLater": "Dans {count} jours", "common_noDueDate": "Pas de date d'échéance", "common_day": "Jour", "common_week": "<PERSON><PERSON><PERSON>", "common_month": "<PERSON><PERSON>", "common_year": "<PERSON><PERSON>", "common_monday": "<PERSON><PERSON>", "common_tuesday": "<PERSON><PERSON>", "common_wednesday": "<PERSON><PERSON><PERSON><PERSON>", "common_thursday": "<PERSON><PERSON>", "common_friday": "<PERSON><PERSON><PERSON><PERSON>", "common_saturday": "<PERSON><PERSON>", "common_sunday": "<PERSON><PERSON><PERSON>", "common_weekdays": "<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>", "common_save": "Enregistrer", "common_cancel": "Annuler", "common_confirm": "Confirmer", "common_delete": "<PERSON><PERSON><PERSON><PERSON>", "common_confirmDelete": "Confirmer la <PERSON>", "common_retry": "<PERSON><PERSON><PERSON><PERSON>", "common_default": "Défaut", "common_unknown": "Inconnu", "common_categoryTask": "Vues de Tâches", "common_categoryTimer": "<PERSON>ues de Minuteur", "common_categoryProject": "Vues de Projets", "common_categoryFunction": "Vues de Fonctions", "common_initializing": "Initialisation...", "common_processing": "Traitement en cours...", "common_loading": "Chargement...", "common_unknownError": "Une erreur inconnue est survenue", "common_specificDate": "Date Spécifique", "common_maxSelection": "Sélectionnez jusqu'à {count} jours", "common_unknownTask": "Tâche inconnue", "initScreen_authenticatingTitle": "Démarrage du moteur...", "initScreen_authenticatingDesc": "<PERSON>rêt à entrer dans votre monde de productivité", "initScreen_refreshingSessionTitle": "Synchronisation...", "initScreen_refreshingSessionDesc": "Vos données restent cohérentes sur tous les appareils", "next1DayView_title": "<PERSON><PERSON><PERSON>'hui", "next1DayView_emptyMessage": "Aucune tâche pour aujourd'hui", "next3DaysView_title": "3 Jours", "next3DaysView_emptyMessage": "Aucune tâche dans les 3 prochains jours", "next7DaysView_title": "7 Jours", "next7DaysView_emptyMessage": "Aucune tâche dans les 7 prochains jours", "inboxView_title": "<PERSON><PERSON><PERSON>", "inboxView_emptyMessage": "Aucune tâche dans votre boîte de réception", "calendarListView_titleCurrentYear": "{date}", "calendarListView_titleOtherYear": "{date}", "calendarListView_emptyMessage": "Aucune tâche ce jour-là", "monthCalendarView_title": "<PERSON><PERSON><PERSON>", "eisenhowerMatrixView_title": "<PERSON><PERSON>", "eisenhowerMatrixView_quadrant1": "Urgent et Important 🚩🔥", "eisenhowerMatrixView_quadrant2": "Non Urgent et Important 🚩", "eisenhowerMatrixView_quadrant3": "Urgent et Non Important 🔥", "eisenhowerMatrixView_quadrant4": "Non Urgent et Non Important", "eisenhowerMatrixView_emptyMessage": "Aucune tâche dans ce quadrant", "todoView_title": "À faire", "todoView_emptyMessage": "Aucune tâche à faire", "overdueView_title": "En retard", "overdueView_emptyMessage": "Aucune tâche en retard", "completedView_title": "Terminées", "completedView_emptyMessage": "Aucune tâche terminée", "droppedView_title": "Abandonnées", "droppedView_emptyMessage": "Aucune tâche abandonnée", "timersView_title": "Minuteurs", "timersView_emptyMessage": "<PERSON><PERSON><PERSON> minuteur", "tasksTimersView_title": "Minuteurs de Tâches", "tasksTimersView_emptyMessage": "<PERSON><PERSON><PERSON> minuteur", "projectsView_title": "Projets", "projectsView_archived": "Projets Archivés", "projectsView_emptyMessage": "Aucun projet", "projectView_projectNotFound": "Projet non trouvé", "projectView_taskCompletedRate": "{completedCount}/{totalCount} terminées", "projectView_emptyMessage": "Aucune tâche dans ce projet", "recurringsView_title": "Tâches Récurrentes", "recurringsView_emptyMessage": "Aucune tâche récurrente", "statisticsView_title": "Statistiques", "statisticsView_emptyMessage": "Aucun graphique de statistiques, veuillez les activer dans les paramètres", "entryView_title": "Entrée", "searchPage_title": "<PERSON><PERSON><PERSON>", "searchPage_hintText": "Rechercher un nom de tâche...", "searchPage_noKeywordMessage": "Veuillez entrer des mots-clés pour rechercher des tâches", "searchPage_noResultFound": "Aucune tâche contenant \"{searchTerm}\" n'a été trouvée", "settingsPage_title": "Paramètres", "settingsPage_notSignedIn": "Non connecté", "settingsPage_notSignedInDesc": "Appuyez pour vous connecter ou vous inscrire", "settingsPage_accountCenter": "Centre de Compte", "settingsPage_accountCenterDesc": "Appuyez pour voir ou modifier", "settingsPage_upgradeToPremium": "Passer à Premium", "settingsPage_upgradeToPremiumDesc": "Débloquez plus de fonctionnalités avancées", "settingsPage_nav": "Barre de Navigation", "settingsPage_navDesc": "Personnalisez votre barre de navigation inférieure", "settingsPage_stat": "Graphique des Statistiques", "settingsPage_statDesc": "Personnalisez vos graphiques de statistiques", "settingsPage_theme": "Thème", "settingsPage_themeDesc": "Choisissez votre thème d'application préféré", "settingsPage_language": "<PERSON><PERSON>", "settingsPage_languageDesc": "Changer la langue d'affichage de l'application", "settingsPage_dateTime": "Date et Heure", "settingsPage_dateTimeDesc": "Définir les formats d'affichage de la date et de l'heure", "settingsPage_taskDisplay": "Affichage des Tâches", "settingsPage_taskDisplayDesc": "Définir comment les tâches sont affichées", "accountPage_title": "Centre de Compte", "accountPage_currentPlan": "Plan Actuel", "accountPage_planMonthly": "<PERSON><PERSON><PERSON>", "accountPage_planYearly": "<PERSON><PERSON>", "accountPage_planLifetime": "À vie", "accountPage_renewsOn": "Renouv. le", "accountPage_expiresOn": "Expire le", "accountPage_joined": "Membre depuis", "accountPage_days": "{days} jours", "accountPage_signUpMethod": "Méthode", "accountPage_changeUsername": "Changer de nom d'utilisateur", "accountPage_signOut": "Se déconnecter", "accountPage_confirmSignOutTitle": "Confirmer la déconnexion", "accountPage_confirmSignOutContent": "Êtes-vous sûr de vouloir vous déconnecter du compte actuel ?", "accountPage_deleteAccount": "<PERSON><PERSON><PERSON><PERSON> le Compte (Dangereux)", "navSettingPage_title": "Paramètres de Navigation", "navSettingPage_enabledNavs": "Éléments de Navigation Activés", "navSettingPage_emptyEnabledNavs": "Aucun élément de navigation activé. Seule la page d'entrée sera affichée.", "navSettingPage_disabledNavs": "Éléments de Navigation Désactivés", "statSettingPage_title": "Paramètres des Graphiques Statistiques", "statSettingPage_enabledStats": "Graphiques Activés", "statSettingPage_emptyEnabledStats": "Aucun graphique activé", "statSettingPage_disabledStats": "Graphiques Désactivés", "statSettingPage_categoryTask": "Statistiques des Tâches", "statSettingPage_categoryTimer": "Statistiques du Minuteur", "statSettingPage_categoryHabit": "Statistiques des Habitudes", "statSettingPage_categoryOther": "Autres Statistiques", "themeSettingPage_title": "Paramètres de Thème", "languageSettingPage_title": "Paramètres de Langue", "languageSettingPage_system": "Système (System)", "dateTimeSettingPage_title": "Paramètres de Date et Heure", "dateTimeSettingPage_weekStart": "La semaine commence le", "taskDisplaySettingPage_title": "Paramètres d'Affichage des Tâches", "taskDisplaySettingPage_showCompleted": "Afficher les tâches terminées", "taskDisplaySettingPage_showCompletedDesc": "Afficher les tâches terminées dans la liste des tâches", "taskDisplaySettingPage_showOverdue": "Afficher les tâches en retard", "taskDisplaySettingPage_showOverdueDesc": "Afficher les tâches en retard dans la liste des tâches", "taskDisplaySettingPage_showDropped": "Afficher les tâches abandonnées", "taskDisplaySettingPage_showDroppedDesc": "Afficher les tâches abandonnées dans la liste des tâches", "signInPage_title": "Se connecter ou S'inscrire", "signInPage_linkSent": "Lien de connexion envoy<PERSON>, veuillez vérifier votre e-mail", "signInPage_linkFailed": "Échec de l'envoi du lien : {message}", "signInPage_errorAccountExists": "Cet e-mail est déjà enregistré via une autre méthode. Veuillez vous connecter avec cette méthode d'abord, puis lier votre compte Google dans les paramètres.", "signInPage_errorUserDisabled": "Ce compte Google a été désactivé. Veuillez contacter le support.", "signInPage_errorOperationNotAllowed": "La connexion Google n'est pas activée. Veuillez contacter l'administrateur.", "signInPage_errorNetworkRequestFailed": "La requête réseau a échoué. Veuillez vérifier votre connexion réseau.", "signInPage_errorDefault": "La connexion Google a échoué. Veuillez réessayer plus tard.", "signInPage_invalidEmail": "Veuillez entrer une adresse e-mail valide", "signInPage_sendLinkButton": "Envoyer le lien de connexion", "signInPage_orDivider": "OU", "signInPage_googleButton": "Se connecter avec Google", "taskTimersPage_title": "Minuteurs de Tâches", "taskTimersPage_taskNotFound": "Informations sur la tâche non trouvées", "taskTimersPage_noTimers": "<PERSON>tte tâche n'a pas de minuteur", "archivedProjectsPage_title": "Projets Archivés", "archivedProjectsPage_emptyMessage": "Aucun projet archivé", "subscribeGuideSheet_projectTitle": "Limite de projets atteinte", "subscribeGuideSheet_projectLimit": "La version gratuite permet de créer jusqu'à {count} projets.", "subscribeGuideSheet_projectBenefit": "Passez à Premium pour créer jusqu'à {count} projets.", "subscribeGuideSheet_recurringTitle": "Limite de tâches récurrentes atteinte", "subscribeGuideSheet_recurringLimit": "La version gratuite permet de créer jusqu'à {count} tâches récurrentes.", "subscribeGuideSheet_recurringBenefit": "Passez à Premium pour créer jusqu'à {count} tâches récurrentes.", "subscribeGuideSheet_timerTitle": "<PERSON><PERSON> de minuteurs atteinte", "subscribeGuideSheet_timerLimit": "La version gratuite permet de créer jusqu'à {count} minuteurs.", "subscribeGuideSheet_timerBenefit": "Passez à Premium pour des minuteurs illimités.", "subscribeGuideSheet_upgradeButton": "Passer à Premium", "subscribeGuideSheet_upgradeError": "Impossible de charger la page d'abonnement : {error}", "subscribeGuideSheet_laterButton": "Peut-être plus tard", "taskEditPage_titleEdit": "Modifier la Tâche", "taskEditPage_titleCreate": "<PERSON><PERSON><PERSON> une Tâche", "taskEditPage_droppedWarning": "Cette tâche a été abandonnée et ne peut pas être modifiée. Pour la modifier, veuillez d'abord la restaurer.", "taskEditPage_recurringWarning": "Cette tâche est une instance d'une tâche récurrente. Les modifications actuelles ne s'appliquent qu'à cette instance.", "taskEditPage_nameHint": "Que vous préparez-vous à faire ?", "taskEditPage_validatorEmpty": "Le nom de la tâche ne peut pas être vide", "taskEditPage_validatorLength": "Le nom de la tâche ne peut pas dépasser {max} caractères", "taskEditPage_timerActionStatusRunning": "Un minuteur est en cours, il ne peut donc pas être cliqué", "taskEditPage_timerActionStatusCompleted": "Les tâches terminées ne peuvent pas être chronométrées", "taskEditPage_timerActionStatusDropped": "Les tâches abandonnées ne peuvent pas être chronométrées", "taskEditPage_timerActionStatusLimitReached": "Vous avez atteint la limite de minuteurs, veuil<PERSON>z vous abonner", "taskEditPage_timerActionStatusReady": "Cliquez pour démarrer le chronométrage de la tâche", "taskDatePicker_noDate": "Pas de date d'échéance", "taskDatePicker_nextMonday": "<PERSON><PERSON> prochain", "taskProjectPicker_noProject": "Aucun projet associé", "taskProjectPicker_emptyMessage": "Pas encore de projets, veuillez en créer un d'abord", "taskProjectPicker_createProject": "Créer un Nouveau Projet", "taskClockPicker_noDueClock": "Pas d'heure d'éch<PERSON>ance", "taskClockPicker_title": "Sélectionner l'heure d'échéance", "taskEventPicker_noEventClock": "Aucun événement programmé", "taskEventPicker_title": "Sélectionner l'heure de l'événement", "taskEventPicker_errorNoStartClock": "<PERSON>euillez sélectionner une heure de début", "taskEventPicker_errorNoEndClock": "Veuillez sélectionner une heure de fin", "taskEventPicker_errorEndClockBeforeStartClock": "L'heure de fin ne peut pas être antérieure à l'heure de début", "taskEventPicker_startClockLabel": "<PERSON><PERSON> d<PERSON>", "taskEventPicker_endClockLabel": "Heure de fin", "taskEventPicker_selectClock": "Choi<PERSON>", "taskLongPressSheet_drop": "Abandonner la Tâche", "taskLongPressSheet_dropDesc": "La tâche deviendra inopérable", "taskLongPressSheet_restore": "Restaurer la Tâche", "taskLongPressSheet_restoreDesc": "La tâche sera restaurée à un état opérable", "taskLongPressSheet_delete": "Supp<PERSON>er la Tâche", "taskLongPressSheet_deleteDesc": "Les minuteurs associés seront également supprimés", "taskLongPressSheet_confirmDeleteTitle": "Confirmer la Suppression", "taskLongPressSheet_confirmDeleteContent": "Êtes-vous sûr de vouloir supprimer définitivement la tâche \"{name}\" ? Cette action est irréversible.", "timerEditSheet_title": "Modifier le Minuteur", "timerEditSheet_startLabel": "Début :", "timerEditSheet_endLabel": "Fin :", "timerEditSheet_deleteTimer": "Supprimer ce minuteur", "timerEditSheet_errorEndClockBeforeStartClock": "L'heure de fin ne peut pas être antérieure à l'heure de début.", "timerEditSheet_confirmDeleteTitle": "Confirmer la Suppression", "timerEditSheet_confirmDeleteContent": "Êtes-vous sûr de vouloir supprimer cet enregistrement de minuteur ? Cette action est irréversible.", "timerClockSheet_title": "Sélectionner l'heure", "projectEditSheet_titleEdit": "Modifier le Projet", "projectEditSheet_titleCreate": "Créer un Projet", "projectEditSheet_nameHint": "Entrez le nom du projet...", "projectEditSheet_validatorEmpty": "Le nom du projet ne peut pas être vide", "projectEditSheet_validatorLength": "Le nom du projet ne peut pas dépasser {max} caractères", "projectLongPressSheet_unarchive": "Désarchiver", "projectLongPressSheet_unarchiveDesc": "Le projet sera déplacé vers la page de liste", "projectLongPressSheet_archive": "Archiver le Projet", "projectLongPressSheet_archiveDesc": "Le projet sera déplacé vers la page des archives", "projectLongPressSheet_deleteProject": "Supprimer le Projet", "projectLongPressSheet_deleteProjectDesc": "Les tâches seront conservées mais ne seront plus associées à ce projet", "projectLongPressSheet_deleteProjectAndTasks": "Supprimer le Projet et les Tâches", "projectLongPressSheet_deleteProjectAndTasksDesc": "Toutes les tâches du projet seront supprimées définitivement", "projectLongPressSheet_confirmDeleteTitle": "Confirmer la Suppression du Projet ?", "projectLongPressSheet_confirmDeleteContent": "Le projet \"{name}\" sera supprimé, mais ses tâches seront conservées (plus associées à ce projet). Cette action est irréversible.", "projectLongPressSheet_deleteFailed": "Échec de la suppression du projet : {error}", "projectLongPressSheet_confirmDeleteWithTasksTitle": "Confirmer la Suppression du Projet et de Toutes les Tâches ?", "projectLongPressSheet_confirmDeleteWithTasksContent": "Le projet \"{name}\" et toutes ses tâches seront supprimés définitivement. Cette action est irréversible.", "projectLongPressSheet_deleteWithTasksFailed": "Échec de la suppression du projet et des tâches : {error}", "recurringEditPage_titleEdit": "Modifier la Tâche Récurrente", "recurringEditPage_titleCreate": "Nouvelle Tâche Récurrente", "recurringEditPage_creationHint": "Les instances de tâches pour les 30 prochains jours seront créées initialement, puis renouvelées quotidiennement.", "recurringEditPage_nameHint": "Que prévoyez-vous de r<PERSON> ?", "recurringEditPage_errorRuleEmpty": "La règle de récurrence ne peut pas être vide", "recurringEditPage_errorSaveFailed": "Échec de la création/mise à jour de la tâche récurrente : {error}", "recurringRulesPicker_noRule": "Pas de règle de ré<PERSON>", "recurringRulesPicker_title": "Définir la Récurrence", "rulesModePicker_title": "Mode de Répétition", "rulesDowPicker_title": "Sélectionner les jours de la semaine", "rulesDowPicker_placeholder": "Sélectionner les jours", "rulesDowPicker_weekdays": "<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>", "rulesDomPicker_title": "Sélectionner les Dates", "rulesDomPicker_placeholder": "Sélectionner les dates", "rulesDoyPicker_title": "Sélectionner les Dates", "rulesDoyPicker_placeholder": "Sélectionner les dates", "rulesStartPicker_title": "Commence le", "rulesStartPicker_placeholder": "Sélectionner la date de début", "rulesEndPicker_title": "Se termine le", "rulesEndPicker_neverEnds": "Ne se termine jamais", "rulesCountPicker_title": "Répétitions", "rulesCountPicker_timesSuffix": "fois", "rulesCountPicker_unlimited": "Illimité", "recurringLongPressSheet_delete": "Supprimer la Tâche Récurrente", "recurringLongPressSheet_deleteDesc": "Supprime définitivement la tâche récurrente et ses instances", "recurringLongPressSheet_confirmDeleteTitle": "Confirmer la Suppression", "recurringLongPressSheet_confirmDeleteContent": "Êtes-vous sûr de vouloir supprimer définitivement la tâche récurrente \"{name}\" ? Cette action est irréversible.", "changeUsernameSheet_title": "Changer <PERSON>'Utilisa<PERSON>ur", "changeUsernameSheet_label": "Nom d'utilisateur", "changeUsernameSheet_validatorEmpty": "Le nom d'utilisateur ne peut pas être vide", "changeUsernameSheet_validatorLength": "Le nom d'utilisateur ne peut pas dépasser {max} caractères", "deleteAccountSheet_title": "<PERSON><PERSON><PERSON><PERSON> le Compte", "deleteAccountSheet_warning": "Cette action est irréversible. Toutes vos données, y compris les tâches, projets, etc., seront supprimées définitivement.", "deleteAccountSheet_confirmTitle": "Êtes-vous sûr ?", "deleteAccountSheet_confirmContent": "Une fois le compte supprimé, il ne pourra pas être récupéré.", "deleteAccountSheet_successed": "Votre compte et toutes les données associées ont été définitivement supprimés. Merci de nous avoir accompagnés. N'hésitez pas à commencer une nouvelle aventure à tout moment !", "todayGlanceWidget_todo": "À faire", "todayGlanceWidget_timer": "<PERSON><PERSON><PERSON>", "todayGlanceWidget_overdue": "En retard", "taskPriorityWidget_important": "Important", "taskPriorityWidget_urgent": "<PERSON><PERSON>", "dateGroupedTasksWidget_empty": "<PERSON><PERSON><PERSON> tâche", "weeklyTaskChart_title": "Achèvement des Tâches de cette Semaine", "weeklyTaskChart_legendDue": "Par Date d'<PERSON>", "weeklyTaskChart_legendCompleted": "Par Date d'Achèvement", "weeklyTimerChart_title": "<PERSON><PERSON><PERSON> du Minuteur de cette Semaine", "habitTaskChart_error": "Échec du chargement des données d'habitude", "habitTimerChart_note": "* Les statistiques sont basées sur la date d'échéance de la tâche, et non sur la date du minuteur.", "navModel_next1DayLabel": "<PERSON><PERSON><PERSON>'hui", "navModel_next1DayName": "Tâches d'Aujourd'hui", "navModel_next1DayDesc": "A<PERSON><PERSON><PERSON> toutes les tâches pour aujourd'hui", "navModel_next3DaysLabel": "3 Jours", "navModel_next3DaysName": "Tâches des 3 Jours", "navModel_next3DaysDesc": "<PERSON><PERSON><PERSON><PERSON> toutes les tâches des 3 prochains jours", "navModel_next7DaysLabel": "7 Jours", "navModel_next7DaysName": "Tâches des 7 Jours", "navModel_next7DaysDesc": "<PERSON><PERSON><PERSON><PERSON> toutes les tâches des 7 prochains jours", "navModel_inboxLabel": "<PERSON><PERSON><PERSON>", "navModel_inboxName": "Tâches de la Boîte de Réception", "navModel_inboxDesc": "<PERSON><PERSON><PERSON><PERSON> toutes les tâches sans date d'échéance ou projet", "navModel_calendarListLabel": "<PERSON><PERSON><PERSON>", "navModel_calendarListName": "Liste du Calendrier", "navModel_calendarListDesc": "A<PERSON><PERSON><PERSON> le calendrier et la liste des tâches", "navModel_monthCalendarLabel": "<PERSON><PERSON><PERSON>", "navModel_monthCalendarName": "<PERSON><PERSON><PERSON>", "navModel_monthCalendarDesc": "Afficher les tâches du calendrier mensuel", "navModel_eisenhowerMatrixLabel": "<PERSON><PERSON>", "navModel_eisenhowerMatrixName": "<PERSON><PERSON>", "navModel_eisenhowerMatrixDesc": "Afficher les tâches de la matrice d'Eisenhower", "navModel_todoLabel": "À faire", "navModel_todoName": "Tâches à faire", "navModel_todoDesc": "A<PERSON><PERSON><PERSON> toutes les tâches non terminées avant leur échéance", "navModel_overdueLabel": "En Retard", "navModel_overdueName": "Tâches en retard", "navModel_overdueDesc": "A<PERSON>iche<PERSON> toutes les tâches non terminées après leur échéance", "navModel_completedLabel": "Terminées", "navModel_completedName": "Tâches terminées", "navModel_completedDesc": "<PERSON><PERSON><PERSON><PERSON> toutes les tâches terminées", "navModel_droppedLabel": "Abandonnées", "navModel_droppedName": "Tâches abandonnées", "navModel_droppedDesc": "A<PERSON>iche<PERSON> toutes les tâches abandonnées", "navModel_timersLabel": "Minuteurs", "navModel_timersName": "Liste des Minuteurs", "navModel_timersDesc": "<PERSON><PERSON>iche<PERSON> tous les minuteurs", "navModel_tasksTimersLabel": "Minuteurs", "navModel_tasksTimersName": "Liste des Minuteurs de Tâches", "navModel_tasksTimersDesc": "Affiche<PERSON> tous les minuteurs de tâches", "navModel_projectsLabel": "Projets", "navModel_projectsName": "Liste des Projets", "navModel_projectsDesc": "Afficher tous les projets", "navModel_projectLabel": "Projet", "navModel_projectName": "{projectName} (Projet)", "navModel_projectDesc": "<PERSON><PERSON><PERSON><PERSON> toutes les tâches de ce projet", "navModel_recurringsLabel": "<PERSON><PERSON><PERSON><PERSON>", "navModel_recurringsName": "Tâches Récurrentes", "navModel_recurringsDesc": "Affiche<PERSON> toutes les tâches récurrentes", "navModel_statisticsLabel": "Stats", "navModel_statisticsName": "Statistiques", "navModel_statisticsDesc": "Afficher toutes les statistiques", "navModel_entryLabel": "Entrée", "navModel_entryName": "Entrée", "navModel_entryDesc": "Afficher tous les raccourcis des vues", "statModel_weeklyTaskName": "Tâches Hebdomadaires", "statModel_weeklyTaskDescription": "Statistiques d'achèvement des tâches quotidiennes de cette semaine", "statModel_weeklyTimerName": "Minuteurs Hebdomadaires", "statModel_weeklyTimerDescription": "Statistiques de durée des minuteurs quotidiens de cette semaine", "statModel_habitTaskName": "{habitName} (<PERSON><PERSON><PERSON>)", "statModel_habitTaskDescription": "Suivi de l'achèvement de l'habitude \"{habitName}\"", "statModel_habitTimerName": "{habitName} (Minuteur)", "statModel_habitTimerDescription": "Suivi de la durée du minuteur pour l'habitude \"{habitName}\"", "statModel_timeProgressName": "Progression du Temps", "statModel_timeProgressDescription": "Statistiques de progression en anneau pour le jour, la semaine, le mois et l'année", "themeModel_forestGreen": "<PERSON><PERSON>", "themeModel_morningMist": "<PERSON><PERSON><PERSON>", "recurringModel_every": "<PERSON><PERSON>", "recurringModel_doyFormat": "d <PERSON><PERSON>", "recurringModel_description": "{every} {interval} {frequency} {dates}", "recurringModel_toDate": " jusqu'au {date}", "recurringModel_toInfinite": " indéfiniment", "recurringModel_repeat": " {count} fois", "appException_template": "<PERSON><PERSON><PERSON> de {how} : {why}", "appException_how_initializeApp": "l'initialisation de l'application", "appException_how_createAccount": "la création du compte", "appException_how_signIn": "la connexion", "appException_how_signOut": "la déconnexion", "appException_how_updateUsername": "la mise à jour du nom d'utilisateur", "appException_how_deleteAccount": "la suppression du compte", "appException_how_createTask": "la création de la tâche", "appException_how_updateTask": "la mise à jour de la tâche", "appException_how_deleteTask": "la suppression de la tâche", "appException_how_createTimer": "la création du minuteur", "appException_how_updateTimer": "la mise à jour du minuteur", "appException_how_deleteTimer": "la suppression du minuteur", "appException_how_createProject": "la création du projet", "appException_how_updateProject": "la mise à jour du projet", "appException_how_deleteProject": "la suppression du projet", "appException_how_createRecurring": "la création de la tâche récurrente", "appException_how_updateRecurring": "la mise à jour de la tâche récurrente", "appException_how_deleteRecurring": "la suppression de la tâche récurrente", "appException_why_unauthenticated": "utilisateur non connecté", "appException_why_permissionDenied": "autorisation refusée", "appException_why_serverError": "une erreur de serveur est survenue", "appException_why_notFound": "ressource non trouvée", "appException_why_alreadyExists": "la ressource existe déjà", "appException_why_invalidArgument": "argument non valide", "appException_why_credentialAlreadyInUse": "informations d'identification déjà utilisées", "appException_why_emailAlreadyInUse": "email déjà utilisé", "appException_why_accountExistsWithDifferentCredential": "compte existant avec des informations d'identification différentes", "appException_why_userDisabled": "compte utilisateur désactivé", "appException_why_operationNotAllowed": "opération non autorisée", "appException_why_networkRequestFailed": "<PERSON><PERSON><PERSON> <PERSON>", "appException_why_operationFailed": "l'opération a échoué", "appException_why_featureLimited": "limite de fonctionnalités du plan gratuit atteinte", "appException_why_unknown": "une erreur inconnue s'est produite"}