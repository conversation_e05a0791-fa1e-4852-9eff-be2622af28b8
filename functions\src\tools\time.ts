import { Timestamp } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';
import { RECURRING } from './config.js';
import type { RecurringTemplate } from './types.js';

/**
 * 时间处理工具类
 * 
 * 时间命名约定：
 * 1. 日期：Date (yyyy-MM-dd)
 * 2. 时刻：Clock (HH:mm:ss)
 * 3. 时间：Time (yyyy-MM-dd HH:mm:ss)
 */
export default class TimeUtils {
  constructor() {}
  
  //////////////////////////////////////////////////
  /// 时区相关方法
  //////////////////////////////////////////////////

  /**
   * 将任意时间转换为该时区的当天零点
   * @param time - 任意时间
   * @returns UTC 时间
   */
  static toStartOfDay(timezone: string, time: Date): Date {
    try {
      const zonedTime = toZonedTime(time, timezone);
      zonedTime.setHours(0, 0, 0, 0);
      return fromZonedTime(zonedTime, timezone);
    } catch (e) {
      logger.warn("toStartOfDay: 无效的时区标识符，将回退到 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      const fallbackDate = new Date(time);
      fallbackDate.setUTCHours(0, 0, 0, 0);
      return fallbackDate;
    }
  }

  /**
   * 将任意时间转换为该时区的当天结束
   * @param time - 任意时间
   * @returns UTC 时间
   */
  static toEndOfDay(timezone: string, time: Date): Date {
    try {
      const zonedDate = toZonedTime(time, timezone);
      zonedDate.setHours(23, 59, 59, 999);
      return fromZonedTime(zonedDate, timezone);
    } catch (e) {
      logger.warn("toEndOfDay: 无效的时区标识符，将回退到 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      const fallbackDate = new Date(time);
      fallbackDate.setUTCHours(23, 59, 59, 999);
      return fallbackDate;
    }
  }

  /**
   * 从 dueDate 数字重建正确的 UTC 日期
   * @param dueDate - 格式如 20250730 的日期数字
   * @returns UTC 日期
   */
  static dueDateToUTC(timezone: string, dueDate: number): Date {
    const dateStr = dueDate.toString().padStart(8, '0');
    const year = parseInt(dateStr.substring(0, 4), 10);
    const month = parseInt(dateStr.substring(4, 6), 10) - 1;
    const day = parseInt(dateStr.substring(6, 8), 10);
    
    try {
      // 在用户时区创建日期，然后转为UTC
      const localDate = new Date();
      localDate.setFullYear(year, month, day);
      localDate.setHours(0, 0, 0, 0);
      return fromZonedTime(localDate, timezone);
    } catch (e) {
      logger.warn("dueDateToUTC: 时区转换失败，使用 UTC", { 
        dueDate, 
        timezone: timezone, 
        error: (e as Error).message 
      });
      return new Date(Date.UTC(year, month, day));
    }
  }

  /**
   * 获取当前时区的今天零点
   * @returns UTC 时间
   */
  static getTodayStart(timezone: string): Date {
    return this.toStartOfDay(timezone, new Date());
  }

  /**
   * 获取指定日期的下一天开始时间
   * @returns UTC 时间
   */
  static getNextDayStart(timezone: string, date: Date): Date {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    return this.toStartOfDay(timezone, nextDay);
  }

  /**
 * 计算任务生成的日期范围
 */
  static calculateGenerationRange(timezone: string): { startDate: Date; endDate: Date } {
    const startDate = this.getTodayStart(timezone);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + RECURRING.GENERATION_DAYS_AHEAD);
    return { startDate, endDate };
  }

  /**
   * 为 rrule 创建标准化的 dtstart（用户时区的日期部分 + UTC午夜）
   * @returns UTC 时间
   */
  static createRRuleDtstart(timezone: string, date: Date): Date {
    try {
      const startTimeInUserTz = toZonedTime(date, timezone);
      return new Date(Date.UTC(
        startTimeInUserTz.getFullYear(),
        startTimeInUserTz.getMonth(),
        startTimeInUserTz.getDate()
      ));
    } catch (e) {
      logger.warn("createRRuleDtstart: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      return new Date(Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate()
      ));
    }
  }

  /**
   * 为 rrule 创建标准化的 until（用户时区的日期部分 + UTC当天结束）
   * @returns UTC 时间
   */
  static createRRuleUntil(timezone: string, date: Date): Date {
    try {
      const untilDateInUserTz = toZonedTime(date, timezone);
      return new Date(Date.UTC(
        untilDateInUserTz.getFullYear(),
        untilDateInUserTz.getMonth(),
        untilDateInUserTz.getDate(),
        23, 59, 59, 999
      ));
    } catch (e) {
      logger.warn("createRRuleUntil: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      return new Date(Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate(),
        23, 59, 59, 999
      ));
    }
  }

  /**
   * 为 rrule.between() 创建标准化的范围参数
   * @returns UTC 时间
   */
  static createRRuleRange(timezone: string, startDate: Date, endDate: Date): { start: Date; end: Date } {
    try {
      const startRangeInUserTz = toZonedTime(startDate, timezone);
      const normalizedRangeStart = new Date(Date.UTC(
        startRangeInUserTz.getFullYear(),
        startRangeInUserTz.getMonth(),
        startRangeInUserTz.getDate()
      ));

      const endRangeInUserTz = toZonedTime(endDate, timezone);
      const normalizedRangeEnd = new Date(Date.UTC(
        endRangeInUserTz.getFullYear(),
        endRangeInUserTz.getMonth(),
        endRangeInUserTz.getDate(),
        23, 59, 59, 999
      ));

      return { start: normalizedRangeStart, end: normalizedRangeEnd };
    } catch (e) {
      logger.warn("createRRuleRange: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      return { start: startDate, end: endDate };
    }
  }

  /**
   * 将 rrule 返回的 UTC 日期转换为用户时区，用于月日比较
   */
  static toMonthDayInTimezone(timezone: string, date: Date): { month: number; day: number } {
    try {
      const zonedDate = toZonedTime(date, timezone);
      return {
        month: zonedDate.getMonth() + 1,
        day: zonedDate.getDate()
      };
    } catch (e) {
      logger.warn("getMonthDayInTimezone: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      return {
        month: date.getUTCMonth() + 1,
        day: date.getUTCDate()
      };
    }
  }

  /**
   * 合并日期和时间，返回用户时区的精确时刻（UTC Timestamp）
   */
  static combineDateClock(timezone: string, timeForDate: Date, timeForClock: Date): Timestamp {
    try {
      const combinedTime = toZonedTime(timeForDate, timezone);
      const clock = toZonedTime(timeForClock, timezone);

      combinedTime.setHours(
        clock.getHours(),
        clock.getMinutes(),
        clock.getSeconds(),
        clock.getMilliseconds()
      );

      return Timestamp.fromDate(fromZonedTime(combinedTime, timezone));
    } catch (e) {
      logger.warn("combineDateTime: 时区转换失败，使用简单合并", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      // 回退逻辑：简单的时间合并
      const combinedTime = new Date(timeForDate);
      combinedTime.setHours(timeForClock.getHours(), timeForClock.getMinutes(), timeForClock.getSeconds());
      return Timestamp.fromDate(combinedTime);
    }
  }

  /**
   * 将日期格式化为 dueDate 数字格式（YYYYMMDD）
   */
  static toNumberedDueDate(timezone: string, date: Date): number {
    try {
      const zonedDate = toZonedTime(date, timezone);
      const year = zonedDate.getFullYear();
      const month = (zonedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = zonedDate.getDate().toString().padStart(2, '0');
      return parseInt(`${year}${month}${day}`, 10);
    } catch (e) {
      logger.warn("formatToDueDate: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      const year = date.getUTCFullYear();
      const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
      const day = date.getUTCDate().toString().padStart(2, '0');
      return parseInt(`${year}${month}${day}`, 10);
    }
  }

  /**
   * 为年度重复规则计算结束日期
   * @param startTime - 规则开始时间
   * @param repeatYears - 重复的年数
   * @returns 最后一年的年末UTC时间
   */
  static calculateYearlyRuleUntil(timezone: string, startTime: Date, repeatYears: number): Date {
    try {
      const startDate = toZonedTime(startTime, timezone);
      // `repeatYears` 是重复的年数。我们在起始年份上加 N-1 得到最后一年的年份。
      const untilYear = startDate.getFullYear() + repeatYears - 1;
      // 结束日期应为最后一年的最后一天。
      return new Date(Date.UTC(untilYear, 11, 31, 23, 59, 59, 999));
    } catch (e) {
      logger.warn("calculateYearlyRuleUntil: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      const untilYear = startTime.getUTCFullYear() + repeatYears - 1;
      return new Date(Date.UTC(untilYear, 11, 31, 23, 59, 59, 999));
    }
  }

  /**
   * 从时间戳提取用户时区下的日期字符串 (YYYY-MM-DD)
   * @param time - UTC 时间戳
   * @returns 格式化的日期字符串
   */
  static toDateStringInTimezone(timezone: string, time: Date): string {
    try {
      const zonedTime = toZonedTime(time, timezone);
      const year = zonedTime.getFullYear();
      const month = (zonedTime.getMonth() + 1).toString().padStart(2, '0');
      const day = zonedTime.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (e) {
      logger.warn("extractDateString: 时区转换失败，使用 UTC", { 
        timezone: timezone, 
        error: (e as Error).message 
      });
      const year = time.getUTCFullYear();
      const month = (time.getUTCMonth() + 1).toString().padStart(2, '0');
      const day = time.getUTCDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
  
  //////////////////////////////////////////////////
  /// 时区无关方法
  //////////////////////////////////////////////////

  /**
   * 判断任务模板是否为全天任务
   */
  static isAllDayTask(template: RecurringTemplate): boolean {
    return !template.dueClock;
  }

  /**
   * 将 Firestore 存储的星期几 (1-7, Mon-Sun) 映射到 rrule 的星期数值
   * @param firestoreDays - Firestore 中的星期数组 (1-7, 周一为1)
   * @returns rrule 库所使用的星期数值数组 (0-6, 周一为0) 或 null
   */
  static mapDaysOfWeekToRRule(firestoreDays: number[] | null): number[] | null {
    if (!firestoreDays || firestoreDays.length === 0) {
      return null;
    }
    // 我们的 Firestore 模型: MO=1, TU=2, ..., SU=7
    // rrule 的星期定义:   MO=0, TU=1, ..., SU=6
    // 因此，只需将 Firestore 的 day - 1 即可。
    return firestoreDays.map(day => day - 1);
  }

  /**
   * 计算每个周期内的发生次数
   */
  static calculateOccurrencesPerCycle(
    frequency: string,
    daysOfWeek?: number[] | null,
    daysOfMonth?: number[] | null
  ): number {
    let occurrencesPerCycle = 1;
    
    if (frequency === 'weekly' && daysOfWeek && daysOfWeek.length > 0) {
      occurrencesPerCycle = daysOfWeek.length;
    } else if (frequency === 'monthly' && daysOfMonth && daysOfMonth.length > 0) {
      occurrencesPerCycle = daysOfMonth.length;
    }
    
    return occurrencesPerCycle;
  }

  /**
   * 将 dueDate 数字转换为日期字符串格式
   */
  static formatDueDateToString(dueDate: number): string {
    const dateStr = dueDate.toString().padStart(8, '0');
    return `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`;
  }
}
