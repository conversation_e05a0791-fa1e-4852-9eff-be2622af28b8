{"name": "functions", "version": "0.3.2", "description": "Taskive Cloud Functions", "type": "module", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "./lib/index.js", "dependencies": {"firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2", "@google-cloud/secret-manager": "^6.0.1", "express": "^5.1.0", "luxon": "^3.4.4", "rrule": "^2.8.1", "date-fns-tz": "^3.2.0"}, "devDependencies": {"typescript": "^5.8.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-plugin-import": "^2.31.0", "@types/node": "^22.15.30", "@types/express": "^5.0.3"}, "private": true}