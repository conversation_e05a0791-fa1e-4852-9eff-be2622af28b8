// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_functions/cloud_functions.dart';
// tools
import 'app_exception.dart';
// others
import '../../generated/l10n/app_localizations.dart';

/// 将 Firebase 及其他的异常映射到我们的 AppException
/// 这是一个全局函数，可被任何与 Firebase 交互的 Service 使用。
AppException appExceptionMapper({
  required Exception error,
  required AppExceptionHow how,
  StackTrace? stack,
}) {
  if (error is FirebaseException) {
    // 对于 Firebase 异常
    final why = appExceptionWhyMapper(error.code);
    final message = error.message;
    return AppException(how: how, why: why, message: message, stack: stack);
  }

  if (error is FirebaseAuthException) {
    // 对于 Firebase Auth 异常
    final why = appExceptionWhyMapper(error.code);
    final message = error.message;
    return AppException(how: how, why: why, message: message, stack: stack);
  }

  if (error is FirebaseFunctionsException) {
    // 对于云函数异常
    final why = appExceptionWhyMapper(error.code);
    final message = error.message;
    return AppException(how: how, why: why, message: message, stack: stack);
  }

  if (error is AppException) {
    // 对于自定义异常
    return error;
  }

  // 对于其他所有未知异常
  return AppException(how: AppExceptionHow.unknown, why: AppExceptionWhy.unknown, message: error.toString());
}

AppExceptionWhy appExceptionWhyMapper(String firebaseErrorCode) {
  switch (firebaseErrorCode) {
    case 'unauthenticated':
      return AppExceptionWhy.unauthenticated;
    case 'permission-denied':
      return AppExceptionWhy.permissionDenied;
    case 'internal': // Firebase 的内部错误
    case 'server-error': // Firestore 'server-error' (虽然不常用，但以防万一)
      return AppExceptionWhy.serverError;
    case 'not-found':
      return AppExceptionWhy.notFound;
    case 'already-exists':
      return AppExceptionWhy.alreadyExists;
    case 'invalid-argument':
      return AppExceptionWhy.invalidArgument;
    case 'credential-already-in-use':
      return AppExceptionWhy.credentialAlreadyInUse;
    case 'email-already-in-use':
      return AppExceptionWhy.emailAlreadyInUse;
    case 'account-exists-with-different-credential':
      return AppExceptionWhy.accountExistsWithDifferentCredential;
    case 'user-disabled':
      return AppExceptionWhy.userDisabled;
    case 'operation-not-allowed':
      return AppExceptionWhy.operationNotAllowed;
    case 'network-request-failed':
      return AppExceptionWhy.networkRequestFailed;
    default:
      // 对于所有其他未明确处理的 Firebase 错误，归类为通用操作失败
      return AppExceptionWhy.operationFailed;
  }
}

// how 可以为空，因为可能没有报错
String appExceptionHowLocaleMapper(BuildContext context, AppExceptionHow? how) {
  if (how == null) return '';

  final l10n = AppLocalizations.of(context)!;

  switch (how) {
    // 初始化
    case AppExceptionHow.initializeApp:
      return l10n.appException_how_initializeApp;
    // account
    case AppExceptionHow.createAccount:
      return l10n.appException_how_createAccount;
    case AppExceptionHow.signIn:
      return l10n.appException_how_signIn;
    case AppExceptionHow.signOut:
      return l10n.appException_how_signOut;
    case AppExceptionHow.updateUsername:
      return l10n.appException_how_updateUsername;
    case AppExceptionHow.deleteAccount:
      return l10n.appException_how_deleteAccount;
    // 任务
    case AppExceptionHow.loadTask:
      return '';
    case AppExceptionHow.createTask:
      return l10n.appException_how_createTask;
    case AppExceptionHow.updateTask:
      return l10n.appException_how_updateTask;
    case AppExceptionHow.deleteTask:
      return l10n.appException_how_deleteTask;
    // 计时
    case AppExceptionHow.loadTimer:
      return '';
    case AppExceptionHow.createTimer:
      return l10n.appException_how_createTimer;
    case AppExceptionHow.updateTimer:
      return l10n.appException_how_updateTimer;
    case AppExceptionHow.deleteTimer:
      return l10n.appException_how_deleteTimer;
    // 项目
    case AppExceptionHow.loadProject:
      return '';
    case AppExceptionHow.createProject:
      return l10n.appException_how_createProject;
    case AppExceptionHow.updateProject:
      return l10n.appException_how_updateProject;
    case AppExceptionHow.deleteProject:
      return l10n.appException_how_deleteProject;
    // 周期任务
    case AppExceptionHow.loadRecurring:
      return '';
    case AppExceptionHow.createRecurring:
      return l10n.appException_how_createRecurring;
    case AppExceptionHow.updateRecurring:
      return l10n.appException_how_updateRecurring;
    case AppExceptionHow.deleteRecurring:
      return l10n.appException_how_deleteRecurring;
    // 设置
    case AppExceptionHow.loadSetting:
      return '';
    case AppExceptionHow.updateSetting:
      return '';
    // 未知
    case AppExceptionHow.unknown:
      return '';
  }
}

// why 可以为空，因为可能没有报错
String appExceptionWhyLocaleMapper(BuildContext context, AppExceptionWhy? why) {
  if (why == null) return '';

  final l10n = AppLocalizations.of(context)!;

  switch (why) {
    case AppExceptionWhy.unauthenticated:
      return l10n.appException_why_unauthenticated;
    case AppExceptionWhy.permissionDenied:
      return l10n.appException_why_permissionDenied;
    case AppExceptionWhy.serverError:
      return l10n.appException_why_serverError;
    case AppExceptionWhy.notFound:
      return l10n.appException_why_notFound;
    case AppExceptionWhy.alreadyExists:
      return l10n.appException_why_alreadyExists;
    case AppExceptionWhy.invalidArgument:
      return l10n.appException_why_invalidArgument;
    case AppExceptionWhy.credentialAlreadyInUse:
      return l10n.appException_why_credentialAlreadyInUse;
    case AppExceptionWhy.emailAlreadyInUse:
      return l10n.appException_why_emailAlreadyInUse;
    case AppExceptionWhy.accountExistsWithDifferentCredential:
      return l10n.appException_why_accountExistsWithDifferentCredential;
    case AppExceptionWhy.userDisabled:
      return l10n.appException_why_userDisabled;
    case AppExceptionWhy.operationNotAllowed:
      return l10n.appException_why_operationNotAllowed;
    case AppExceptionWhy.networkRequestFailed:
      return l10n.appException_why_networkRequestFailed;
    case AppExceptionWhy.operationFailed:
      return l10n.appException_why_operationFailed;
    case AppExceptionWhy.featureLimited:
      return l10n.appException_why_featureLimited;
    case AppExceptionWhy.unknown:
      return l10n.appException_why_unknown;
  }
}
