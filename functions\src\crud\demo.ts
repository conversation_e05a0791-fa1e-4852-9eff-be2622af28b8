import { Timestamp } from 'firebase-admin/firestore';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';
import { db, DATABASE } from '../tools/config.js';
import { TaskModel, TimerModel } from '../tools/types.js';
import TimeUtils from '../tools/time.js';


/**
 * 获取一个指定日期，在用户时区下的特定时刻，并将其转换为可存储的UTC时间戳
 * @param timezone - 用户的IANA时区标识符
 * @param dateObject - 代表目标日期的 Date 对象 (例如今天或昨天)
 * @param hour - 您期望在用户时区下的小时 (0-23)
 * @param minute - 您期望在用户时区下的分钟 (0-59)
 * @returns 一个标准的、可直接存入 Firestore 的 UTC Date 对象
 */
function getLocalTimeAsUTC(timezone: string, dateObject: Date, hour: number, minute: number): Date {
  const zonedDate = toZonedTime(dateObject, timezone);
  zonedDate.setHours(hour, minute, 0, 0);
  return fromZonedTime(zonedDate, timezone);
}


/**
 * 为新用户创建一组固定的、发生在“昨天”的引导数据
 * 这个函数保证了所有用户看到的初始状态完全一致
 * @param uid - 用户的 UID
 * @param userDocRef - 用户的 Firestore 文档引用
 * @param timezone - 用户的 IANA 时区标识符
 */
export async function createDemoDataForNewUser(
  uid: string,
  userDocRef: FirebaseFirestore.DocumentReference,
  timezone: string,
) {
  const batch = db.batch();

  // --- 1. 设定“今天”的时间基准 ---
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  // --- 2. 定义“昨天”的固定时间点 ---
  // 任务 2 的第一个计时
  const task2_timer1_start = getLocalTimeAsUTC(timezone, yesterday, 9, 15);
  const task2_timer1_end = getLocalTimeAsUTC(timezone, yesterday, 10, 28); // 73分钟
  
  // 任务 1 的第一个计时
  const task1_timer1_start = getLocalTimeAsUTC(timezone, yesterday, 14, 46);
  const task1_timer1_end = getLocalTimeAsUTC(timezone, yesterday, 15, 27); // 41分钟

  // 任务 2 的第二个计时
  const task2_timer2_start = getLocalTimeAsUTC(timezone, yesterday, 16, 30);
  const task2_timer2_end = getLocalTimeAsUTC(timezone, yesterday, 16, 56); // 26分钟

  // --- 3. 预先生成所有文档的引用 ---
  const tasksColRef = userDocRef.collection(DATABASE.TASKS_COLLECTION);
  const timersColRef = userDocRef.collection(DATABASE.TIMERS_COLLECTION);

  const task1Ref = tasksColRef.doc();
  const task2Ref = tasksColRef.doc();
  const task3Ref = tasksColRef.doc();

  // --- 4. 准备要写入批处理的数据 ---

  // --- 任务 1 ---

  // 任务 1: 欢迎任务 (未完成, 截止于今天, 1个计时)
  const task1Data: TaskModel = {
    userId: uid,
    createTime: Timestamp.now(),
    updateTime: Timestamp.now(),
    name: "Welcome to Taskive 🎉, your space to:\n- Tackle your to-dos\n- Track your time with timers\nLong-press a card for more options.",
    timezone: timezone,
    dueDate: TimeUtils.toNumberedDueDate(timezone, new Date()), // 截止日期是今天
    dueTime: null,
    projectId: null,
    recurringId: null,
    isImportant: false,
    isUrgent: false,
    completeTime: null,
    dropTime: null,
  };

  // 计时器 1 (关联任务1，昨天下午)
  const timer1Data: TimerModel = {
    userId: uid,
    taskId: task1Ref.id,
    createTime: Timestamp.fromDate(task1_timer1_start),
    updateTime: Timestamp.fromDate(task1_timer1_end),
    timezone: timezone,
    startTime: Timestamp.fromDate(task1_timer1_start),
    endTime: Timestamp.fromDate(task1_timer1_end),
    isRunning: false,
  };

  // --- 任务 2 ---

  const noonTodayInUTC = getLocalTimeAsUTC(timezone, new Date(), 12, 0);
  
  // 任务 2: 已完成任务 (截止于今天中午, 2个计时)
  const task2Data: TaskModel = {
    userId: uid,
    createTime: Timestamp.now(),
    updateTime: Timestamp.now(),
    name: "One task down ✅, one step closer to your goals.",
    timezone: timezone,
    dueDate: null,
    dueTime: Timestamp.fromDate(noonTodayInUTC),
    projectId: null,
    recurringId: null,
    isImportant: false,
    isUrgent: false,
    completeTime: Timestamp.now(), // 完成时间是现在
    dropTime: null,
  };

  // 计时器 2 (关联任务2，昨天上午)
  const timer2Data: TimerModel = {
    userId: uid,
    taskId: task2Ref.id,
    createTime: Timestamp.fromDate(task2_timer1_start),
    updateTime: Timestamp.fromDate(task2_timer1_end),
    timezone: timezone,
    startTime: Timestamp.fromDate(task2_timer1_start),
    endTime: Timestamp.fromDate(task2_timer1_end),
    isRunning: false,
  };
  
  // 计时器 3 (关联任务2，昨天下午)
  const timer3Data: TimerModel = {
    userId: uid,
    taskId: task2Ref.id,
    createTime: Timestamp.fromDate(task2_timer2_start),
    updateTime: Timestamp.fromDate(task2_timer2_end),
    timezone: timezone,
    startTime: Timestamp.fromDate(task2_timer2_start),
    endTime: Timestamp.fromDate(task2_timer2_end),
    isRunning: false,
  };

  // --- 任务 3 ---

  // 任务 3: 已丢弃任务 (截止于今天, 无计时)
  const task3Data: TaskModel = {
    userId: uid,
    createTime: Timestamp.now(),
    updateTime: Timestamp.now(),
    name: "Declutter your mind. Dropping a task 🫳 gives you permission to let go.",
    timezone: timezone,
    dueDate: TimeUtils.toNumberedDueDate(timezone, new Date()),
    dueTime: null,
    projectId: null,
    recurringId: null,
    isImportant: false,
    isUrgent: false,
    completeTime: null,
    dropTime: Timestamp.now(), // 丢弃时间是现在
  };

  // --- 5. 将所有数据添加到批处理中 ---
  batch.set(task1Ref, task1Data);
  batch.set(task2Ref, task2Data);
  batch.set(task3Ref, task3Data);
  
  batch.set(timersColRef.doc(), timer1Data);
  batch.set(timersColRef.doc(), timer2Data);
  batch.set(timersColRef.doc(), timer3Data);

  // --- 6. 一次性提交所有写入操作 ---
  await batch.commit();
}
