// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// import 'package:equatable/equatable.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../models/project_model.dart';
// views
/* 任务 */
import '../views/next_1day_view.dart';
import '../views/next_3days_view.dart';
import '../views/next_7days_view.dart';
import '../views/inbox_view.dart';
import '../views/calendar_list_view.dart';
import '../views/month_calendar_view.dart';
import '../views/eisenhower_matrix_view.dart';
import '../views/todo_view.dart';
import '../views/overdue_view.dart';
import '../views/completed_view.dart';
import '../views/dropped_view.dart';
/* 计时 */
import '../views/timers_view.dart';
import '../views/tasks_timers_view.dart';
/* 项目 */
import '../views/projects_view.dart';
import '../views/project_view.dart';
/* 功能 */
import '../views/recurrings_view.dart';
import '../views/statistics_view.dart';
import '../views/entry_view.dart';
// others
import '../generated/l10n/app_localizations.dart';

// --- Enums ---

enum NavCategory {
  task,
  timer,
  project,
  function,
}

// --- Base Definition ---

/// 基础定义模型，包含所有静态、非本地化的数据。
/// 这是导航项的"蓝图"，在整个应用中应该是单例且不可变的。
class NavDefinition {
  final String id;
  final NavCategory category;
  final IconData icon;
  final bool isProject;
  final bool isFixed;
  final Widget Function() widgetBuilder;

  const NavDefinition({
    required this.id,
    required this.category,
    required this.icon,
    this.isProject = false,
    this.isFixed = false,
    required this.widgetBuilder,
  });

  // --- Static Definitions ---

  static final next1day = NavDefinition(
    id: 'W8qE4rT0yU6iO2pA9sD5f',
    category: NavCategory.task,
    icon: LucideIcons.coffee,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const Next1DayView(),
  );

  static final next3days = NavDefinition(
    id: 'O7qL3eT8nH2sK9dJ5vC1x',
    category: NavCategory.task,
    icon: LucideIcons.coffee,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const Next3DaysView(),
  );

  static final next7days = NavDefinition(
    id: 'E3rT9yU5iO1pA7sD4fG0h',
    category: NavCategory.task,
    icon: LucideIcons.coffee,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const Next7DaysView(),
  );

  static final inbox = NavDefinition(
    id: 'T7yU4iO1pA6sD3fG9hJ2k',
    category: NavCategory.task,
    icon: LucideIcons.inbox,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const InboxView(),
  );

  static final calendarList = NavDefinition(
    id: 'C7vB4nM0qW6eR2tY8uI5o',
    category: NavCategory.task,
    icon: LucideIcons.galleryThumbnails,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const CalendarListView(),
  );

  static final monthCalendar = NavDefinition(
    id: 'F2gH8jK5lM1nB7vC3xZ9q',
    category: NavCategory.task,
    icon: LucideIcons.calendar,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const MonthCalendarView(),
  );

  static final eisenhowerMatrix = NavDefinition(
    id: 'P2oI5uY8tR3eW6qA9sD1f',
    category: NavCategory.task,
    icon: LucideIcons.grid2x2,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const EisenhowerMatrixView(),
  );

  static final todo = NavDefinition(
    id: 'M6nB3vC9xZ5qW1eR7tY4u',
    category: NavCategory.task,
    icon: LucideIcons.circle,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const TodoView(),
  );

  static final overdue = NavDefinition(
    id: 'S9aD6fG3hJ0kL5mN2pQ8w',
    category: NavCategory.task,
    icon: LucideIcons.circleAlert,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const OverdueView(),
  );

  static final completed = NavDefinition(
    id: 'X5zL2cV8bN4mQ0wE7rT3y',
    category: NavCategory.task,
    icon: LucideIcons.circleCheck,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const CompletedView(),
  );

  static final dropped = NavDefinition(
    id: 'D8fG5hJ1kL7mN3pQ9wE4r',
    category: NavCategory.task,
    icon: LucideIcons.circleSlash,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const DroppedView(),
  );

  static final timers = NavDefinition(
    id: 'H9jK4lM1nB6vC3xZ0qW7e',
    category: NavCategory.timer,
    icon: LucideIcons.timer,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const TimersView(),
  );

  static final tasksTimers = NavDefinition(
    id: 'G5hJ8kL2mN6pQ0wE3rT9y',
    category: NavCategory.timer,
    icon: LucideIcons.timer,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const TasksTimersView(),
  );

  static final projects = NavDefinition(
    id: 'Z1xC4vB7nM0qW9eR6tY3u',
    category: NavCategory.project,
    icon: LucideIcons.folderTree,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const ProjectsView(),
  );

  static final recurrings = NavDefinition(
    id: 'N8qV2bM5xY1rT7cW9e0u',
    category: NavCategory.function,
    icon: LucideIcons.repeat,
    isProject: false,
    isFixed: false,    
    widgetBuilder: () => const RecurringsView(),
  );

  static final statistics = NavDefinition(
    id: 'B6vF3mK9pT2rX7jL4sZ0d',
    category: NavCategory.function,
    icon: LucideIcons.chartPie,
    isProject: false,
    isFixed: false,
    widgetBuilder: () => const StatisticsView(),
  );

  static final entry = NavDefinition(
    id: 'Q1nT5xL2vM6pC9yR1dZ3',
    category: NavCategory.function,
    icon: LucideIcons.grip,
    isProject: false,
    isFixed: true,
    widgetBuilder: () => const EntryView(),
  );
  
  /// 所有固定导航项的定义列表
  static final List<NavDefinition> definitions = [
    next1day,
    next3days,
    next7days,
    inbox,
    calendarList,
    monthCalendar,
    eisenhowerMatrix,
    todo,
    overdue,
    completed,
    dropped,
    timers,
    tasksTimers,
    projects,
    recurrings,
    statistics,
    entry,
  ];

  /// 所有固定导航项定义的 Map, 以 ID 为 key
  static final Map<String, NavDefinition> definitionMap = {
    for (var def in definitions) def.id: def
  };

  // @override
  // List<Object?> get props => [id];
}

// --- Dynamic Model ---

/// `NavModel` 现在是一个包含已本地化和动态数据的轻量级、具体模型。
/// 它由 `NavState` 创建和管理，并在 UI 层中用于渲染。
class NavModel {
  final String id;
  final NavCategory category;
  final IconData icon;
  final Color? color;
  final bool isProject;
  final bool isFixed;
  final Widget page;
  String label;
  String name;
  String description;

  NavModel({
    required this.id,
    required this.category,
    required this.icon,
    this.color,
    this.isProject = false,
    this.isFixed = false,
    required this.page,
    this.label = '',
    this.name = '',
    this.description = '',
  });

  // --- Static Getters for IDs ---

  /// 默认显示的导航ID列表
  static List<String> get defaultNavIds => [
    NavDefinition.next1day.id,
    NavDefinition.timers.id,
    NavDefinition.projects.id,
  ];

  // --- 工厂构造函数 ---

  /// 从 `NavDefinition` 创建一个 `NavModel`
  factory NavModel.fromDefinition(NavDefinition def) {
    return NavModel(
      id: def.id,
      category: def.category,
      icon: def.icon,
      isProject: def.isProject,
      isFixed: def.isFixed,
      page: def.widgetBuilder(),
    );
  }

  /// 从 `ProjectModel` 创建一个 `NavModel`
  factory NavModel.fromProject(ProjectModel project) {
    return NavModel(
      id: project.id,
      category: NavCategory.project,
      icon: LucideIcons.folder,
      color: project.color,
      isProject: true,
      isFixed: false,
      page: ProjectView(projectId: project.id),
    );
  }

  /// 使用 l10n 对象重建 UI 文本
  void rebuild(AppLocalizations l10n, {String? projectName}) {
    if (isProject) {
      label = projectName ?? '';
      name = l10n.navModel_projectName(projectName ?? '');
      description = l10n.navModel_projectDesc;
    } else {
      if (id == NavDefinition.next1day.id) {
        label = l10n.navModel_next1DayLabel;
        name = l10n.navModel_next1DayName;
        description = l10n.navModel_next1DayDesc;
      }

      else if (id == NavDefinition.next3days.id) {
        label = l10n.navModel_next3DaysLabel;
        name = l10n.navModel_next3DaysName;
        description = l10n.navModel_next3DaysDesc;
      }
      
      else if (id == NavDefinition.next7days.id) {
        label = l10n.navModel_next7DaysLabel;
        name = l10n.navModel_next7DaysName;
        description = l10n.navModel_next7DaysDesc;
      }
      
      else if (id == NavDefinition.inbox.id) {
        label = l10n.navModel_inboxLabel;
        name = l10n.navModel_inboxName;
        description = l10n.navModel_inboxDesc;
      }
      
      else if (id == NavDefinition.calendarList.id) {
        label = l10n.navModel_calendarListLabel;
        name = l10n.navModel_calendarListName;
        description = l10n.navModel_calendarListDesc;
      }
      
      else if (id == NavDefinition.monthCalendar.id) {
        label = l10n.navModel_monthCalendarLabel;
        name = l10n.navModel_monthCalendarName;
        description = l10n.navModel_monthCalendarDesc;
      }
      
      else if (id == NavDefinition.eisenhowerMatrix.id) {
        label = l10n.navModel_eisenhowerMatrixLabel;
        name = l10n.navModel_eisenhowerMatrixName;
        description = l10n.navModel_eisenhowerMatrixDesc;
      }
      
      else if (id == NavDefinition.todo.id) {
        label = l10n.navModel_todoLabel;
        name = l10n.navModel_todoName;
        description = l10n.navModel_todoDesc;
      }
      
      else if (id == NavDefinition.overdue.id) {
        label = l10n.navModel_overdueLabel;
        name = l10n.navModel_overdueName;
        description = l10n.navModel_overdueDesc;
      }
      
      else if (id == NavDefinition.completed.id) {
        label = l10n.navModel_completedLabel;
        name = l10n.navModel_completedName;
        description = l10n.navModel_completedDesc;
      }
      
      else if (id == NavDefinition.dropped.id) {
        label = l10n.navModel_droppedLabel;
        name = l10n.navModel_droppedName;
        description = l10n.navModel_droppedDesc;
      }
      
      else if (id == NavDefinition.timers.id) {
        label = l10n.navModel_timersLabel;
        name = l10n.navModel_timersName;
        description = l10n.navModel_timersDesc;
      }
      
      else if (id == NavDefinition.tasksTimers.id) {
        label = l10n.navModel_tasksTimersLabel;
        name = l10n.navModel_tasksTimersName;
        description = l10n.navModel_tasksTimersDesc;
      }
      
      else if (id == NavDefinition.projects.id) {
        label = l10n.navModel_projectsLabel;
        name = l10n.navModel_projectsName;
        description = l10n.navModel_projectsDesc;
      }
      
      else if (id == NavDefinition.recurrings.id) {
        label = l10n.navModel_recurringsLabel;
        name = l10n.navModel_recurringsName;
        description = l10n.navModel_recurringsDesc;
      }
      
      else if (id == NavDefinition.statistics.id) {
        label = l10n.navModel_statisticsLabel;
        name = l10n.navModel_statisticsName;
        description = l10n.navModel_statisticsDesc;
      }
      
      else if (id == NavDefinition.entry.id) {
        label = l10n.navModel_entryLabel;
        name = l10n.navModel_entryName;
        description = l10n.navModel_entryDesc;
      }
    }
  }

  // @override
  // List<Object?> get props => [
  //   id,
  //   category,
  //   icon,
  //   color,
  //   isProject,
  //   isFixed,
  //   page,
  //   label,
  //   name,
  //   description,
  // ];
}
