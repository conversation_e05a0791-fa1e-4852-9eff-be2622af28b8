// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
// others
import '../generated/l10n/app_localizations.dart';


/// ------ String 扩展 ------
extension StringCasingExtension on String {
  // 首字母大写
  String toCapitalized() => length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
}


String _twoDigits(int n) => n.toString().padLeft(2, '0');

/// ------ DateTime 扩展 ------
extension DateTimeExtension on DateTime {
  /// 格式化日期（04-01 或 2025-04-01）
  String formatDate() {
    // 日期格式
    final dateFormat = DateFormat('MM-dd');

    // 检查是否需要显示年份 (不是当前年份时显示)
    final year = this.year;
    final thisYear = DateTime.now().year;
    final bool showYear = year != thisYear;

    final String dateText = (showYear ? '$year-' : '') + dateFormat.format(this);

    return dateText;
  }

  /// 格式化时钟（09:23）
  String formatClock() {
    // 时间格式
    final timeFormat = DateFormat('HH:mm');

    final clockText = timeFormat.format(this);

    return clockText;
  }

  /// 格式化时间跨度（09:23~10:46）
  String formatSpan(DateTime endTime) {
    // 时间格式
    final timeFormat = DateFormat('HH:mm');

    final startTimeText = timeFormat.format(this);
    final endTimeText = timeFormat.format(endTime);

    final timeSpanText = '$startTimeText~$endTimeText';

    return timeSpanText;
  }

  /// 格式化星期
  String formatWeekday(AppLocalizations l10n) {
    final weekdays = l10n.common_weekdays.split(',');
    // 转换为0-6的索引
    final weekdayIndex = weekday - 1;

    return weekdays[weekdayIndex];
  }

  /// 获取当天的起始时间（00:00:00.000）
  DateTime get startOfDay => DateTime(year, month, day);

  /// 获取当天的结束时间 (23:59:59.999)
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59, 999, 999);

  /// 判断是否同一天
  bool isSameDay(DateTime? other) {
    if (other == null) return false;
    return year == other.year && month == other.month && day == other.day;
  }

  /// 将日期的时间部分设置为零时零分零秒
  DateTime getDateOnly() {
    return DateTime(year, month, day);
  }

  /// 获取相对日期（明天，32 天后）
  String getRelativeDateText(AppLocalizations l10n) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final yesterday = today.subtract(const Duration(days: 1));

    if (isAtSameMomentAs(today)) {
      return l10n.common_today;
    } else if (isAtSameMomentAs(tomorrow)) {
      return l10n.common_tomorrow;
    } else if (isAtSameMomentAs(yesterday)) {
      return l10n.common_yesterday;
    }

    final difference = this.difference(today).inDays;

    if (difference > 0) {
      return l10n.common_daysLater(difference.abs());
    } else {
      return l10n.common_daysAgo(difference.abs());
    }
  }

  /// 获取本周的所有日期
  List<DateTime> getDatesOfThisWeek(int weekStartDay) {
    List<DateTime> weekDates = [];
    DateTime firstDay = subtract(Duration(days: weekday - weekStartDay));
    if (weekday < weekStartDay) {
      firstDay = firstDay.subtract(const Duration(days: 7));
    }
    firstDay = DateTime(firstDay.year, firstDay.month, firstDay.day);

    for (int i = 0; i < 7; i++) {
      weekDates.add(firstDay.add(Duration(days: i)));
    }
    return weekDates;
  }
}

/// ------ Duration 扩展 ------
extension DurationExtension on Duration {
  /// 将秒数格式化为完整时间字符串 (HH:MM:SS)
  String formatLong() {
    String twoDigitHours = _twoDigits(inHours);
    String twoDigitMinutes = _twoDigits(inMinutes.remainder(60));
    String twoDigitSeconds = _twoDigits(inSeconds.remainder(60));
    return "$twoDigitHours:$twoDigitMinutes:$twoDigitSeconds";
  }

  /// 将秒数格式化为时间字符串 (HH:MM)
  String format() {
    String twoDigitHours = _twoDigits(inHours);
    String twoDigitMinutes = _twoDigits(inMinutes.remainder(60));
    return "$twoDigitHours:$twoDigitMinutes";
  }

  /// 将秒数格式化为带单位的时间字符串 (例如：2h45m 或 30m)
  String formatWithUnit() {
    final hours = inHours;
    final minutes = inMinutes.remainder(60);

    if (hours > 0) {
      return '${hours}h${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}

/// ------ int 扩展 (作为秒数) ------
extension IntExtension on int {
  /// 将总秒数转换为时间
  Map<String, int> secondsToTime() {
    final hours = this ~/ 3600;
    final minutes = (this % 3600) ~/ 60;
    final seconds = this % 60;

    return {
      'hours': hours,
      'minutes': minutes,
      'seconds': seconds,
    };
  }

  /// 将秒数格式化为完整时间字符串 (HH:MM:SS)
  String formatLong() {
    final time = secondsToTime();
    final hours = time['hours']!.toString().padLeft(2, '0');
    final minutes = time['minutes']!.toString().padLeft(2, '0');
    final seconds = time['seconds']!.toString().padLeft(2, '0');

    return '$hours:$minutes:$seconds';
  }

  /// 将秒数格式化为时间字符串 (HH:MM)
  String format() {
    final time = secondsToTime();
    final hours = time['hours']!.toString().padLeft(2, '0');
    final minutes = time['minutes']!.toString().padLeft(2, '0');

    return '$hours:$minutes';
  }

  /// 将秒数格式化为带单位的时间字符串 (例如：2h45m 或 30m)
  String formatWithUnit() {
    final time = secondsToTime();
    final hours = time['hours']!;
    final minutes = time['minutes']!;

    if (hours > 0) {
      return '${hours}h${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}

/// ------ BuildContext 扩展 ------
extension BuildContextExtension on BuildContext {
  /// 获取内容视图可用高度
  double getViewHeight() {
    // 获取屏幕尺寸信息
    final screenHeight = MediaQuery.of(this).size.height;
    // 获取 AppBar 高度
    final appBarHeight = AppBar().preferredSize.height;
    // 假设 NavBar 高度
    final navBarHeight = kBottomNavigationBarHeight;
    // 获取顶部状态栏高度
    final paddingTopHeight = MediaQuery.of(this).padding.top;
    // 获取底部设备安全区域高度
    final paddingBottomHeight = MediaQuery.of(this).padding.bottom;

    // 内容视图高度
    return screenHeight - appBarHeight - navBarHeight - paddingTopHeight - paddingBottomHeight;
  }
}
