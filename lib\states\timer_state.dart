// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
// models
import '../models/timer_model.dart';
import '../models/user_model.dart';
// services
import '../services/timer_service.dart';
import '../services/permission_service.dart';
// tools
import '../tools/explicit_value.dart';
import '../tools/extensions.dart';
import '../tools/app_exception_mapper.dart';
import '../tools/app_exception.dart';

// 计时器状态枚举
enum TimerStatus {
  initial, // 初始状态
  loading, // 加载中
  loaded,  // 加载完成
  error,   // 发生错误
}

// 不可变的状态类，包含计时器列表、运行中的计时器等信息
@immutable
class TimerState {
  final TimerStatus status; // 当前状态
  final List<TimerModel> timers; // 所有计时器列表 (包括已完成和可能正在运行的)
  final TimerModel? runningTimer; // 当前正在运行的计时器 (如果有)
  final AppException? streamError; // 错误信息

  /// ====== 构造函数 ======

  const TimerState({
    this.status = TimerStatus.initial,
    this.timers = const [],
    this.runningTimer,
    this.streamError,
  });

  /// ====== 公共状态 ======

  // getter: 检查是否有活动中的计时器
  bool get hasRunningTimer => runningTimer != null;

  /// ====== 工具方法 ======

  // 创建状态副本的方法，允许部分更新
  TimerState copyWith({
    TimerStatus? status,
    List<TimerModel>? timers,
    ExplicitValue<TimerModel?>? runningTimer,
    AppException? streamError,
    bool clearError = false, // 标记是否清除错误信息
  }) {
    return TimerState(
      status: status ?? this.status,
      timers: timers ?? this.timers,
      runningTimer: runningTimer == null ? this.runningTimer : runningTimer.value,
      streamError: clearError ? null : streamError ?? this.streamError,
    );
  }

  // --- 计时筛选 ---

  // 获取所有已结束的计时器 (按开始时间降序)
  List<TimerModel> getAllTimers() {
    final endedTimers = timers.where((timer) => !timer.isRunning).toList();

    // 按开始时间降序排序
    endedTimers.sort((a, b) => b.startTime.compareTo(a.startTime));

    return endedTimers;
  }

  // 通过 id 获取计时器 (假设 id 唯一)
  TimerModel getTimerbyId(String timerId) {
    return timers.firstWhere((timer) => timer.id == timerId);
  }

  // 检查指定任务是否有正在运行的计时器
  bool isThisTaskHasRunningTimer(String taskId) {
    if (runningTimer == null) return false;
    return taskId == runningTimer!.taskId;
  }

  // 获取指定任务的所有已结束计时器 (按开始时间降序)
  List<TimerModel> getTimersForTask(String taskId) {
    final taskTimers = timers.where((timer) => timer.taskId == taskId && !timer.isRunning).toList();

    // 按开始时间降序排序
    taskTimers.sort((a, b) => b.startTime.compareTo(a.startTime));

    return taskTimers;
  }

  // --- 计时统计 ---

  // 获取指定任务的已结束计时器数量
  int getTimerCountForTask(String taskId) {
    return timers.where((timer) => timer.taskId == taskId && !timer.isRunning).length;
  }

  // 获取指定任务的已结束计时器总时长 (秒)
  int getTotalSecondsForTask(String taskId) {
    return timers
        .where((timer) => timer.taskId == taskId && !timer.isRunning)
        .fold(0, (sum, timer) => sum + timer.durationSeconds);
  }

  // 获取指定日期范围内的已结束计时器
  List<TimerModel> getTimersInDateRange(DateTime start, DateTime end) {
    return timers
      .where((timer) => 
        (timer.startTime.isAfter(start) || timer.startTime.isAtSameMomentAs(start)) && 
        (timer.startTime.isBefore(end) || timer.startTime.isAtSameMomentAs(end)) &&
        !timer.isRunning)
      .toList();
  }

  // 获取今日总计时时长
  Duration getTodayTotalTimerDuration() {
    final today = DateTime.now();
    int totalSeconds = 0;

    for (final timer in timers) {
      // 筛选开始时间是今天且已结束的计时器
      if (!timer.isRunning && today.isSameDay(timer.startTime)) {
        totalSeconds += timer.durationSeconds;
      }
    }
    return Duration(seconds: totalSeconds);
  }
}

// Timer 状态管理器，使用 ChangeNotifier 通知 UI 更新
class TimerNotifier extends ChangeNotifier {
  final TimerService _timerService;
  final PermissionService _permissionService;

  /// ====== 私有状态 ======

  StreamSubscription<List<TimerModel>>? _timersSubscription;
  UserModel? _currentUser;
  TimerState _state = const TimerState();

  /// ====== 构造函数 ======

  TimerNotifier(this._timerService, this._permissionService);

  /// ====== 公共状态 ======

  TimerState get state => _state;

  /// ====== 流监听 ======

  /// 当 UserState 发生变化时，由 ProxyProvider 调用
  /// 这是管理数据订阅的唯一入口
  void listenToDependencyChanges(UserModel? user) {
    final oldUserId = _currentUser?.id;
    final newUserId = user?.id;

    if (oldUserId == newUserId) return;

    _timersSubscription?.cancel();
    _timersSubscription = null;
    _currentUser = user;
    _state = const TimerState();
    notifyListeners();

    if (newUserId != null) {
      _listenToTimerChanges(newUserId);
    }
  }

  // 初始化指定用户的计时器 Firestore 流监听
  void _listenToTimerChanges(String userId) {
    if (userId.isEmpty) return;

    _state = _state.copyWith(status: TimerStatus.loading); // 设置为加载状态
    notifyListeners();

    // 开始监听 TimerService 提供的流
    _timersSubscription = _timerService.getTimerStream(userId).listen(
      (timersFromStream) {
        // 从流数据中查找正在运行的计时器
        TimerModel? runningTimer;
        try {
          // 使用 firstWhere 查找第一个 isRunning 为 true 的计时器
          runningTimer = timersFromStream.firstWhere((timer) => timer.isRunning);
        } catch (e) {
          // 如果找不到 (没有正在运行的计时器)，则 runningTimer 为 null
          runningTimer = null;
        }

        // 更新状态：使用来自流的数据
        _state = _state.copyWith(
          status: TimerStatus.loaded, // 设置为加载完成状态
          timers: timersFromStream, // 更新计时器列表
          runningTimer: ExplicitValue(runningTimer), // 更新当前运行的计时器
          clearError: true, // 清除之前的错误信息
        );
        notifyListeners(); // 通知 UI 更新
      },
      onError: (error, stack) {
        // 无需处理，这是预期的行为
        if (error.code == 'permission-denied') return;

        // 处理计时器流错误
        _state = _state.copyWith(
          status: TimerStatus.error,
          streamError: appExceptionMapper(
            error: error as Exception,
            how: AppExceptionHow.loadTimer,
            stack: stack,
          ),
        );
        notifyListeners();
      },
    );
  }

  /// ====== 网络请求 ======

  // 开始一个新的计时器
  Future<void> startTimer(String taskId) async {
    final user = _currentUser;

    if (user == null || user.id.isEmpty) {
      throw AppException(
        how: AppExceptionHow.createTimer,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    // 防止重复启动
    if (_state.hasRunningTimer) {
      throw AppException(
        how: AppExceptionHow.createTimer,
        why: AppExceptionWhy.operationFailed,
        message: 'a timer is already running',
      );
    }

    final now = DateTime.now();
    final String timezone = await FlutterTimezone.getLocalTimezone();

    // 创建新的 TimerModel 实例 (ID 由 Firestore 生成)
    final newTimer = TimerModel(
      id: '', // ID 留空，由 Firestore 自动生成
      userId: user.id,
      taskId: taskId,
      createTime: now,
      updateTime: now,
      timezone: timezone,
      startTime: now,
      endTime: now, // 正在计时的 endTime 只是占位符
      isRunning: true,
    );

    // 使用权限服务执行操作
    await _permissionService.executeWithPermission(
      feature: Feature.timer,
      currentUsage: _state.timers.length,
      currentUser: user,
      action: () => _timerService.createTimer(user.id, newTimer),
    );
  }

  // 停止当前正在运行的计时器
  Future<void> stopTimer() async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateTimer,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (!_state.hasRunningTimer) {
      throw AppException(
        how: AppExceptionHow.updateTimer,
        why: AppExceptionWhy.operationFailed,
        message: 'no running timer',
      );
    }

    final now = DateTime.now();
    final timerToStop = _state.runningTimer!; // 获取当前运行的计时器

    // 创建更新后的计时器模型
    final endedTimer = timerToStop.copyWith(
      updateTime: ExplicitValue(now),
      endTime: ExplicitValue(now), // 设置结束时间
      isRunning: ExplicitValue(false), // 标记为已结束
    );

    // 调用服务更新, 不捕获异常，由 UI 层处理
    await _timerService.updateTimer(userId, endedTimer);
  }

  // 更新一个已存在的计时器 (通常用于编辑已结束的计时器)
  Future<void> updateTimer(TimerModel timer) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateTimer,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (timer.userId != userId) {
      throw AppException(
        how: AppExceptionHow.updateTimer,
        why: AppExceptionWhy.permissionDenied,
      );
    }

    if (timer.id.isEmpty) return;

    await _timerService.updateTimer(userId, timer);
  }

  // 删除一个计时器
  Future<void> deleteTimer(String timerId) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.deleteTimer,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (timerId.isEmpty) return;

    await _timerService.deleteTimer(timerId);
  }

  /// ====== 工具方法 ======

  /// 检查用户是否可以创建新的计时 (用于 UI)
  bool canStartTimer() {
    final user = _currentUser;

    if (user == null) return false;

    return _permissionService.canPerformAction(
      Feature.timer,
      _state.timers.length, // 使用已创建的计时总数作为当前用量
      user,
    );
  }

  // 清除错误信息
  void clearError() {
    _state = _state.copyWith(clearError: true);
    notifyListeners();
  }

  @override
  void dispose() {
    _timersSubscription?.cancel();
    super.dispose();
  }
}
