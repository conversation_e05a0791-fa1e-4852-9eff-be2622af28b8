// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// models
import '../models/timer_model.dart';
// states
import '../states/timer_state.dart';
// tools
import '../tools/config.dart';
import '../tools/extensions.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/timer/timer_card.dart';
// others
import '../generated/l10n/app_localizations.dart';

class TimersView extends StatelessWidget {
  const TimersView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppHeaderBar(
        title: Text(l10n.timersView_title),
      ),
      body: Consumer<TimerNotifier>(
        builder: (context, timerNotifier, child) {
          // 检查计时状态
          if (timerNotifier.state.status == TimerStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          } else if (timerNotifier.state.timers.where((timer) => !timer.isRunning).isEmpty) {
            return Center(child: Text(l10n.timersView_emptyMessage));
          }

          // 获取所有非运行中的计时
          final timers = timerNotifier.state.getAllTimers();

          // 按日期对计时进行分组
          final Map<String, List<TimerModel>> groupedTimers = {};
          final now = DateTime.now();
          final today = DateTime(now.year, now.month, now.day);
          final yesterday = today.subtract(const Duration(days: 1));

          for (final timer in timers) {
            final timerDate = DateTime(
              timer.startTime.year,
              timer.startTime.month,
              timer.startTime.day,
            );

            String groupKey;
            if (timerDate == today) {
              groupKey = l10n.common_today;
            } else if (timerDate == yesterday) {
              groupKey = l10n.common_yesterday;
            } else {
              // 计算天数差
              final difference = today.difference(timerDate).inDays;
              groupKey = l10n.common_daysAgo(difference);
            }

            if (!groupedTimers.containsKey(groupKey)) {
              groupedTimers[groupKey] = [];
            }
            groupedTimers[groupKey]?.add(timer);
          }

          // 对分组键进行排序，确保"今天"在最上面
          final sortedGroups = groupedTimers.keys.toList()..sort((a, b) {
            if (a == l10n.common_today) return -1;
            if (b == l10n.common_today) return 1;
            if (a == l10n.common_yesterday) return -1;
            if (b == l10n.common_yesterday) return 1;
            // 提取数字部分进行比较
            final numA = int.tryParse(a.split('天')[0]) ?? 0;
            final numB = int.tryParse(b.split('天')[0]) ?? 0;
            return numA.compareTo(numB);
          });

          // 构建分组列表视图
          return ListView.builder(
            itemCount: sortedGroups.length + 1,
            itemBuilder: (context, index) {
              if (index == sortedGroups.length) {
                return SizedBox(height: Config.app.bottomSpace);
              }

              final groupKey = sortedGroups[index];
              final timersInGroup = groupedTimers[groupKey] ?? [];

              // 对组内计时按开始时间倒序排序（最新的在前面）
              timersInGroup.sort((a, b) => b.startTime.compareTo(a.startTime));

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 组标题和组内计时总时长
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 左侧：组标题
                        Text(
                          groupKey,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        // 右侧：组内计时总时长
                        Text(
                          _calculateGroupDuration(timersInGroup).format(),
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                  // 组内计时列表
                  ...timersInGroup.map((timer) => TimerCard(timer: timer)),
                ],
              );
            },
          );
        },
      ),
    );
  }

  /// 计算分组内计时的总时长
  int _calculateGroupDuration(List<TimerModel> timers) {
    int totalSeconds = 0;
    for (final timer in timers) {
      totalSeconds += timer.durationSeconds;
    }
    return totalSeconds;
  }
}
