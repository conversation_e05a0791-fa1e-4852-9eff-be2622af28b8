{"@@locale": "en", "common_today": "Today", "@common_today": {"description": "任务截止日期为今天时显示的文字，或者回到今天按钮的文字"}, "common_tomorrow": "Tomorrow", "@common_tomorrow": {"description": "任务截止日期为明天时显示的文字"}, "common_yesterday": "Yesterday", "@common_yesterday": {"description": "任务截止日期为昨天时显示的文字"}, "common_daysAgo": "{count} days ago", "@common_daysAgo": {"description": "任务截止日期为若干天以前时显示的文字", "placeholders": {"count": {"type": "int"}}}, "common_daysLater": "{count} days later", "@common_daysLater": {"description": "任务截止日期为若干天以后时显示的文字", "placeholders": {"count": {"type": "int"}}}, "common_noDueDate": "No due date", "@common_noDueDate": {"description": "任务没有截止日期时显示的文字"}, "common_day": "Day", "@common_day": {"description": "天的单位"}, "common_week": "Week", "@common_week": {"description": "周的单位"}, "common_month": "Month", "@common_month": {"description": "月的单位"}, "common_year": "Year", "@common_year": {"description": "年的单位"}, "common_monday": "Monday", "@common_monday": {"description": "周一"}, "common_tuesday": "Tuesday", "@common_tuesday": {"description": "周二"}, "common_wednesday": "Wednesday", "@common_wednesday": {"description": "周三"}, "common_thursday": "Thursday", "@common_thursday": {"description": "周四"}, "common_friday": "Friday", "@common_friday": {"description": "周五"}, "common_saturday": "Saturday", "@common_saturday": {"description": "周六"}, "common_sunday": "Sunday", "@common_sunday": {"description": "周日"}, "common_weekdays": "<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>", "@common_weekdays": {"description": "一周七天的文字，用逗号分隔"}, "common_save": "Save", "@common_save": {"description": "通用文案，表示保存"}, "common_cancel": "Cancel", "@common_cancel": {"description": "通用文案，表示取消"}, "common_confirm": "Confirm", "@common_confirm": {"description": "通用文案，表示确认"}, "common_delete": "Delete", "@common_delete": {"description": "通用文案，表示删除"}, "common_confirmDelete": "Confirm Delete", "@common_confirmDelete": {"description": "通用文案，表示确认删除"}, "common_retry": "Retry", "@common_retry": {"description": "通用文案，表示重试"}, "common_default": "<PERSON><PERSON><PERSON>", "@common_default": {"description": "通用文案，表示默认"}, "common_unknown": "Unknown", "@common_unknown": {"description": "通用文案，表示未知"}, "common_categoryTask": "Task Views", "@common_categoryTask": {"description": "入口页面中，任务分类的文字说明"}, "common_categoryTimer": "Timer Views", "@common_categoryTimer": {"description": "入口页面中，计时器分类的文字说明"}, "common_categoryProject": "Project Views", "@common_categoryProject": {"description": "入口页面中，项目分类的文字说明"}, "common_categoryFunction": "Function Views", "@common_categoryFunction": {"description": "入口页面中，功能分类的文字说明"}, "common_initializing": "Initializing...", "@common_initializing": {"description": "正在进行页面或数据的初始化"}, "common_processing": "Processing...", "@common_processing": {"description": "正在进行页面或数据的处理"}, "common_loading": "Loading...", "@common_loading": {"description": "数据或页面正在加载中"}, "common_unknownError": "An unknown error occurred", "@common_unknownError": {"description": "发生未知错误，一般发生在请求数据失败时"}, "common_specificDate": "Specific Date", "@common_specificDate": {"description": "周期任务规则选择页面中，选择具体日期提示"}, "common_maxSelection": "Select up to {count} days", "@common_maxSelection": {"description": "周期任务规则选择页面中，选择星期最大个数提示", "placeholders": {"count": {"type": "int"}}}, "common_unknownTask": "Unknown Task", "@common_unknownTask": {"description": "任务不存在时显示的文字"}, "initScreen_authenticatingTitle": "Firing up the engine...", "@initScreen_authenticatingTitle": {"description": "应用首次初始化的全局信息提示标题"}, "initScreen_authenticatingDesc": "Ready to enter your world of productivity", "@initScreen_authenticatingDesc": {"description": "应用首次初始化的全局信息提示描述"}, "initScreen_refreshingSessionTitle": "Syncing your account...", "@initScreen_refreshingSessionTitle": {"description": "应用会话刷新的全局信息提示标题"}, "initScreen_refreshingSessionDesc": "Keeping your data consistent across all devices", "@initScreen_refreshingSessionDesc": {"description": "应用会话刷新的全局信息提示描述"}, "next1DayView_title": "Today", "@next1DayView_title": {"description": "今日任务页面标题"}, "next1DayView_emptyMessage": "No tasks for today", "@next1DayView_emptyMessage": {"description": "今日任务页面空状态提示"}, "next3DaysView_title": "3 Days", "@next3DaysView_title": {"description": "未来7天任务页面标题"}, "next3DaysView_emptyMessage": "No tasks in the next 3 days", "@next3DaysView_emptyMessage": {"description": "未来3天任务页面空状态提示"}, "next7DaysView_title": "7 Days", "@next7DaysView_title": {"description": "未来7天任务页面标题"}, "next7DaysView_emptyMessage": "No tasks in the next 7 days", "@next7DaysView_emptyMessage": {"description": "未来7天任务页面空状态提示"}, "inboxView_title": "Inbox", "@inboxView_title": {"description": "收集箱页面标题"}, "inboxView_emptyMessage": "No tasks in your inbox", "@inboxView_emptyMessage": {"description": "收集箱页面空状态提示"}, "calendarListView_titleCurrentYear": "{date}", "@calendarListView_titleCurrentYear": {"description": "日历列表页面中，当所选日期在当前年份时显示的标题，只显示月份", "placeholders": {"date": {"type": "DateTime", "format": "MMMM"}}}, "calendarListView_titleOtherYear": "{date}", "@calendarListView_titleOtherYear": {"description": "日历列表页面中，当所选日期不在当前年份时显示的标题，显示月份和年份", "placeholders": {"date": {"type": "DateTime", "format": "yMMM"}}}, "calendarListView_emptyMessage": "No tasks on this day", "@calendarListView_emptyMessage": {"description": "日历列表页面空状态提示"}, "monthCalendarView_title": "Calendar", "@monthCalendarView_title": {"description": "日历页面标题"}, "eisenhowerMatrixView_title": "<PERSON>", "@eisenhowerMatrixView_title": {"description": "艾森豪威尔矩阵页面标题"}, "eisenhowerMatrixView_quadrant1": "U & I 🚩🔥", "@eisenhowerMatrixView_quadrant1": {"description": "艾森豪威尔矩阵页面中，紧急且重要象限的标题"}, "eisenhowerMatrixView_quadrant2": "Not U & I 🚩", "@eisenhowerMatrixView_quadrant2": {"description": "艾森豪威尔矩阵页面中，不紧急且重要象限的标题"}, "eisenhowerMatrixView_quadrant3": "U & Not I 🔥", "@eisenhowerMatrixView_quadrant3": {"description": "艾森豪威尔矩阵页面中，紧急且不重要象限的标题"}, "eisenhowerMatrixView_quadrant4": "Not U & Not I", "@eisenhowerMatrixView_quadrant4": {"description": "艾森豪威尔矩阵页面中，不紧急且不重要象限的标题"}, "eisenhowerMatrixView_emptyMessage": "No tasks in this quadrant", "@eisenhowerMatrixView_emptyMessage": {"description": "艾森豪威尔矩阵页面中，某个象限空状态提示"}, "todoView_title": "Todo", "@todoView_title": {"description": "待办任务 (未结束未逾期) 页面标题"}, "todoView_emptyMessage": "No todo tasks", "@todoView_emptyMessage": {"description": "待办任务页面空状态提示"}, "overdueView_title": "Overdue", "@overdueView_title": {"description": "逾期任务 (未结束已逾期) 页面标题"}, "overdueView_emptyMessage": "No overdue tasks", "@overdueView_emptyMessage": {"description": "逾期任务页面空状态提示"}, "completedView_title": "Completed", "@completedView_title": {"description": "已完成任务 (已结束已完成) 页面标题"}, "completedView_emptyMessage": "No completed tasks", "@completedView_emptyMessage": {"description": "已完成任务页面空状态提示"}, "droppedView_title": "Dropped", "@droppedView_title": {"description": "已丢弃任务 (已结束已丢弃) 页面标题"}, "droppedView_emptyMessage": "No dropped tasks", "@droppedView_emptyMessage": {"description": "已丢弃任务页面空状态提示"}, "timersView_title": "Timers", "@timersView_title": {"description": "计时列表页面标题"}, "timersView_emptyMessage": "No timers", "@timersView_emptyMessage": {"description": "计时列表页面空状态提示"}, "tasksTimersView_title": "Tasks Timers", "@tasksTimersView_title": {"description": "任务计时列表页面标题"}, "tasksTimersView_emptyMessage": "No timers", "@tasksTimersView_emptyMessage": {"description": "任务计时列表页面空状态提示"}, "projectsView_title": "Projects", "@projectsView_title": {"description": "项目列表页面标题"}, "projectsView_archived": "Archived Projects", "@projectsView_archived": {"description": "项目列表页面归档按钮的文字"}, "projectsView_emptyMessage": "No projects", "@projectsView_emptyMessage": {"description": "项目列表页面空状态提示"}, "projectView_projectNotFound": "Project not found", "@projectView_projectNotFound": {"description": "项目页面中，项目不存在时显示的文字"}, "projectView_taskCompletedRate": "{completedCount}/{totalCount} completed", "@projectView_taskCompletedRate": {"description": "项目页面中，任务完成比例的文字", "placeholders": {"completedCount": {"type": "int"}, "totalCount": {"type": "int"}}}, "projectView_emptyMessage": "No tasks in this project", "@projectView_emptyMessage": {"description": "项目页面中，项目下没有任务时显示的文字"}, "recurringsView_title": "Recurring Tasks", "@recurringsView_title": {"description": "周期任务页面标题"}, "recurringsView_emptyMessage": "No recurring tasks", "@recurringsView_emptyMessage": {"description": "周期任务页面空状态提示"}, "statisticsView_title": "Statistics", "@statisticsView_title": {"description": "统计页面标题"}, "statisticsView_emptyMessage": "No statistics charts, please enable them in settings", "@statisticsView_emptyMessage": {"description": "统计页面空状态提示"}, "entryView_title": "Entry", "@entryView_title": {"description": "入口页面标题"}, "searchPage_title": "Search", "@searchPage_title": {"description": "搜索页面标题"}, "searchPage_hintText": "Search task name...", "@searchPage_hintText": {"description": "搜索页面中，搜索框的提示文字"}, "searchPage_noKeywordMessage": "Please enter keywords to search for tasks", "@searchPage_noKeywordMessage": {"description": "搜索页面中，搜索框的初始提示文字"}, "searchPage_noResultFound": "No tasks found containing \"{searchTerm}\"", "@searchPage_noResultFound": {"description": "搜索页面中，没有找到包含 {searchTerm} 的任务时显示的文字", "placeholders": {"searchTerm": {"type": "String"}}}, "settingsPage_title": "Settings", "@settingsPage_title": {"description": "设置页面标题"}, "settingsPage_notSignedIn": "Not signed in", "@settingsPage_notSignedIn": {"description": "设置页面中，用户未登录时显示的文字"}, "settingsPage_notSignedInDesc": "Tap to sign in or sign up", "@settingsPage_notSignedInDesc": {"description": "设置页面中，用户未登录时显示的文字"}, "settingsPage_accountCenter": "Account Center", "@settingsPage_accountCenter": {"description": "设置页面中，账户中心标题"}, "settingsPage_accountCenterDesc": "Tap to view or edit", "@settingsPage_accountCenterDesc": {"description": "设置页面中，账户中心描述"}, "settingsPage_upgradeToPremium": "Upgrade to Premium", "@settingsPage_upgradeToPremium": {"description": "设置页面中，升级到高级版标题"}, "settingsPage_upgradeToPremiumDesc": "Unlock more advanced features", "@settingsPage_upgradeToPremiumDesc": {"description": "设置页面中，解锁更多高级功能标题"}, "settingsPage_nav": "Navigation Bar", "@settingsPage_nav": {"description": "设置页面中，导航栏自定义标题"}, "settingsPage_navDesc": "Customize your bottom navigation bar", "@settingsPage_navDesc": {"description": "设置页面中，导航栏自定义描述"}, "settingsPage_stat": "Statistics Chart", "@settingsPage_stat": {"description": "设置页面中，统计图表自定义标题"}, "settingsPage_statDesc": "Customize your statistics charts", "@settingsPage_statDesc": {"description": "设置页面中，统计图表自定义描述"}, "settingsPage_theme": "Theme", "@settingsPage_theme": {"description": "设置页面中，主题设置标题"}, "settingsPage_themeDesc": "Choose your favorite app theme", "@settingsPage_themeDesc": {"description": "设置页面中，主题设置描述"}, "settingsPage_language": "Language", "@settingsPage_language": {"description": "设置页面中的语言设置标题"}, "settingsPage_languageDesc": "Change the application display language", "@settingsPage_languageDesc": {"description": "设置页面中的语言设置描述"}, "settingsPage_dateTime": "Date and Time", "@settingsPage_dateTime": {"description": "设置页面中，日期和时间标题"}, "settingsPage_dateTimeDesc": "Set date and time display formats", "@settingsPage_dateTimeDesc": {"description": "设置页面中，日期和时间描述"}, "settingsPage_taskDisplay": "Task Display", "@settingsPage_taskDisplay": {"description": "设置页面中，任务显示标题"}, "settingsPage_taskDisplayDesc": "Set how tasks are displayed", "@settingsPage_taskDisplayDesc": {"description": "设置页面中，任务显示描述"}, "accountPage_title": "Account Center", "@accountPage_title": {"description": "账户中心页面"}, "accountPage_currentPlan": "Current Plan", "@accountPage_currentPlan": {"description": "账户中心页面中，当前方案标题"}, "accountPage_planMonthly": "Monthly", "@accountPage_planMonthly": {"description": "账户中心页面中，当前方案描述，月度计划"}, "accountPage_planYearly": "Yearly", "@accountPage_planYearly": {"description": "账户中心页面中，当前方案描述，年度计划"}, "accountPage_planLifetime": "Lifetime", "@accountPage_planLifetime": {"description": "账户中心页面中，当前方案描述，终身计划"}, "accountPage_renewsOn": "Renews on", "@accountPage_renewsOn": {"description": "账户中心页面中，自动续订日期标题"}, "accountPage_expiresOn": "Expires on", "@accountPage_expiresOn": {"description": "账户中心页面中，到期日期标题"}, "accountPage_joined": "Joined", "@accountPage_joined": {"description": "账户中心页面中，注册天数标题"}, "accountPage_days": "{days} days", "@accountPage_days": {"description": "账户中心页面中，注册天数描述", "placeholders": {"days": {"type": "int"}}}, "accountPage_signUpMethod": "Method", "@accountPage_signUpMethod": {"description": "账户中心页面中，注册方式标题"}, "accountPage_changeUsername": "Change Username", "@accountPage_changeUsername": {"description": "账户中心页面中，更改用户名标题"}, "accountPage_signOut": "Sign Out", "@accountPage_signOut": {"description": "账户中心页面中，退出登录标题"}, "accountPage_confirmSignOutTitle": "Confirm Sign Out", "@accountPage_confirmSignOutTitle": {"description": "账户中心页面中，退出登录确认标题"}, "accountPage_confirmSignOutContent": "Are you sure you want to sign out of the current account?", "@accountPage_confirmSignOutContent": {"description": "账户中心页面中，退出登录确认内容"}, "accountPage_deleteAccount": "Delete Account (Dangerous)", "@accountPage_deleteAccount": {"description": "账户中心页面中，删除账户标题"}, "navSettingPage_title": "Navigation Settings", "@navSettingPage_title": {"description": "导航设置页面标题"}, "navSettingPage_enabledNavs": "Enabled Navs", "@navSettingPage_enabledNavs": {"description": "导航设置页面中，已启用的导航项标题"}, "navSettingPage_emptyEnabledNavs": "No enabled navigation items. Only the entry page will be shown.", "@navSettingPage_emptyEnabledNavs": {"description": "导航设置页面中，已启用的导航项空状态提示"}, "navSettingPage_disabledNavs": "Disabled Navs", "@navSettingPage_disabledNavs": {"description": "导航设置页面中，未启用的导航项标题"}, "statSettingPage_title": "Statistics Chart Settings", "@statSettingPage_title": {"description": "统计图设置页面标题"}, "statSettingPage_enabledStats": "Enabled Charts", "@statSettingPage_enabledStats": {"description": "统计图设置页面中，已启用的统计图标题"}, "statSettingPage_emptyEnabledStats": "No enabled charts", "@statSettingPage_emptyEnabledStats": {"description": "统计图设置页面中，已启用的统计图空状态提示"}, "statSettingPage_disabledStats": "Disabled Charts", "@statSettingPage_disabledStats": {"description": "统计图设置页面中，未启用的统计图标题"}, "statSettingPage_categoryTask": "Task Stats", "@statSettingPage_categoryTask": {"description": "统计图设置页面中，任务统计标题"}, "statSettingPage_categoryTimer": "Timer Stats", "@statSettingPage_categoryTimer": {"description": "统计图设置页面中，计时统计标题"}, "statSettingPage_categoryHabit": "Habit Stats", "@statSettingPage_categoryHabit": {"description": "统计图设置页面中，习惯统计标题"}, "statSettingPage_categoryOther": "Other Stats", "@statSettingPage_categoryOther": {"description": "统计图设置页面中，其他统计标题"}, "themeSettingPage_title": "Theme Settings", "@themeSettingPage_title": {"description": "主题设置页面标题"}, "languageSettingPage_title": "Language Settings", "@languageSettingPage_title": {"description": "语言设置页面标题"}, "languageSettingPage_system": "System", "@languageSettingPage_system": {"description": "语言设置页面中的'跟随系统'选项"}, "dateTimeSettingPage_title": "Date and Time Settings", "@dateTimeSettingPage_title": {"description": "日期和时间设置页面标题"}, "dateTimeSettingPage_weekStart": "Week starts on", "@dateTimeSettingPage_weekStart": {"description": "日期和时间设置页面中，周开始日期标题"}, "taskDisplaySettingPage_title": "Task Display Settings", "@taskDisplaySettingPage_title": {"description": "任务显示设置页面标题"}, "taskDisplaySettingPage_showCompleted": "Show completed tasks", "@taskDisplaySettingPage_showCompleted": {"description": "任务显示设置页面中，显示已完成任务标题"}, "taskDisplaySettingPage_showCompletedDesc": "Display completed tasks in the task list", "@taskDisplaySettingPage_showCompletedDesc": {"description": "任务显示设置页面中，显示已完成任务描述"}, "taskDisplaySettingPage_showOverdue": "Show overdue tasks", "@taskDisplaySettingPage_showOverdue": {"description": "任务显示设置页面中，显示已过期任务标题"}, "taskDisplaySettingPage_showOverdueDesc": "Display overdue tasks in the task list", "@taskDisplaySettingPage_showOverdueDesc": {"description": "任务显示设置页面中，显示已过期任务描述"}, "taskDisplaySettingPage_showDropped": "Show dropped tasks", "@taskDisplaySettingPage_showDropped": {"description": "任务显示设置页面中，显示已丢弃任务标题"}, "taskDisplaySettingPage_showDroppedDesc": "Display dropped tasks in the task list", "@taskDisplaySettingPage_showDroppedDesc": {"description": "任务显示设置页面中，显示已丢弃任务描述"}, "signInPage_title": "Sign In or Sign Up", "@signInPage_title": {"description": "登录或注册页面标题"}, "signInPage_linkSent": "Sign-in link sent, please check your email", "@signInPage_linkSent": {"description": "登录或注册页面中，发送登录链接成功提示"}, "signInPage_linkFailed": "Failed to send link: {message}", "@signInPage_linkFailed": {"description": "登录或注册页面中，发送登录链接失败提示", "placeholders": {"message": {"type": "String"}}}, "signInPage_errorAccountExists": "This email is already registered through another method. Please log in with that method first and then link your Google account in settings.", "@signInPage_errorAccountExists": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_errorUserDisabled": "This Google account has been disabled. Please contact support.", "@signInPage_errorUserDisabled": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_errorOperationNotAllowed": "Google sign-in is not enabled. Please contact the administrator.", "@signInPage_errorOperationNotAllowed": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_errorNetworkRequestFailed": "Network request failed. Please check your network connection.", "@signInPage_errorNetworkRequestFailed": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_errorDefault": "Google sign-in failed. Please try again later.", "@signInPage_errorDefault": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_invalidEmail": "Please enter a valid email address", "@signInPage_invalidEmail": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_sendLinkButton": "Send Sign-in Link", "@signInPage_sendLinkButton": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_orDivider": "OR", "@signInPage_orDivider": {"description": "登录或注册页面中，发送登录链接失败提示"}, "signInPage_googleButton": "Sign-in with Google", "@signInPage_googleButton": {"description": "登录或注册页面中，发送登录链接失败提示"}, "taskTimersPage_title": "Task Timers", "@taskTimersPage_title": {"description": "计时列表页面标题"}, "taskTimersPage_taskNotFound": "Task information not found", "@taskTimersPage_taskNotFound": {"description": "计时列表页面中，任务信息未找到提示"}, "taskTimersPage_noTimers": "This task has no timers", "@taskTimersPage_noTimers": {"description": "计时列表页面中，任务没有计时提示"}, "archivedProjectsPage_title": "Archived Projects", "@archivedProjectsPage_title": {"description": "已归档项目页面标题"}, "archivedProjectsPage_emptyMessage": "No archived projects", "@archivedProjectsPage_emptyMessage": {"description": "已归档项目页面空状态提示"}, "subscribeGuideSheet_projectTitle": "Project limit reached", "@subscribeGuideSheet_projectTitle": {"description": "订阅引导页面中，项目数量已达上限提示"}, "subscribeGuideSheet_projectLimit": "The free version allows creating up to {count} projects.", "@subscribeGuideSheet_projectLimit": {"description": "订阅引导页面中，项目数量已达上限提示", "placeholders": {"count": {"type": "int"}}}, "subscribeGuideSheet_projectBenefit": "Upgrade to Premium to create up to {count} projects.", "@subscribeGuideSheet_projectBenefit": {"description": "订阅引导页面中，项目数量已达上限提示", "placeholders": {"count": {"type": "int"}}}, "subscribeGuideSheet_recurringTitle": "Recurring task limit reached", "@subscribeGuideSheet_recurringTitle": {"description": "订阅引导页面中，周期任务数量已达上限提示"}, "subscribeGuideSheet_recurringLimit": "The free version allows creating up to {count} recurring tasks.", "@subscribeGuideSheet_recurringLimit": {"description": "订阅引导页面中，周期任务数量已达上限提示", "placeholders": {"count": {"type": "int"}}}, "subscribeGuideSheet_recurringBenefit": "Upgrade to Premium to create up to {count} recurring tasks.", "@subscribeGuideSheet_recurringBenefit": {"description": "订阅引导页面中，周期任务数量已达上限提示", "placeholders": {"count": {"type": "int"}}}, "subscribeGuideSheet_timerTitle": "Timer limit reached", "@subscribeGuideSheet_timerTitle": {"description": "订阅引导页面中，计时数量已达上限提示"}, "subscribeGuideSheet_timerLimit": "The free version allows creating up to {count} timers.", "@subscribeGuideSheet_timerLimit": {"description": "订阅引导页面中，计时数量已达上限提示", "placeholders": {"count": {"type": "int"}}}, "subscribeGuideSheet_timerBenefit": "Upgrade to Premium for unlimited timers.", "@subscribeGuideSheet_timerBenefit": {"description": "订阅引导页面中，计时数量已达上限提示"}, "subscribeGuideSheet_upgradeButton": "Upgrade to Premium", "@subscribeGuideSheet_upgradeButton": {"description": "订阅引导页面中，订阅页面加载失败提示"}, "subscribeGuideSheet_upgradeError": "Could not load subscription page: {error}", "@subscribeGuideSheet_upgradeError": {"description": "订阅引导页面中，订阅页面加载失败提示", "placeholders": {"error": {"type": "String"}}}, "subscribeGuideSheet_laterButton": "Maybe Later", "@subscribeGuideSheet_laterButton": {"description": "订阅引导页面中，订阅页面加载失败提示"}, "taskEditPage_titleEdit": "Edit Task", "@taskEditPage_titleEdit": {"description": "任务编辑页面中，编辑任务标题"}, "taskEditPage_titleCreate": "Create Task", "@taskEditPage_titleCreate": {"description": "任务编辑页面中，创建任务标题"}, "taskEditPage_droppedWarning": "This task has been dropped and cannot be edited. To edit, please restore it first.", "@taskEditPage_droppedWarning": {"description": "任务编辑页面中，已丢弃任务横幅提示"}, "taskEditPage_recurringWarning": "This task is an instance of a recurring task. Current changes only apply to this instance.", "@taskEditPage_recurringWarning": {"description": "任务编辑页面中，周期任务横幅提示"}, "taskEditPage_nameHint": "What are you preparing to do?", "@taskEditPage_nameHint": {"description": "任务编辑页面中，任务名称输入框提示"}, "taskEditPage_validatorEmpty": "Task name cannot be empty", "@taskEditPage_validatorEmpty": {"description": "任务编辑页面中，任务名称不能为空提示"}, "taskEditPage_validatorLength": "Task name cannot exceed {max} characters", "@taskEditPage_validatorLength": {"description": "任务编辑页面中，任务名称长度超出提示", "placeholders": {"max": {"type": "int"}}}, "taskEditPage_timerActionStatusRunning": "A timer is currently running, so it cannot be clicked", "@taskEditPage_timerActionStatusRunning": {"description": "任务编辑页面中，计时状态提示"}, "taskEditPage_timerActionStatusCompleted": "Completed tasks cannot be timed", "@taskEditPage_timerActionStatusCompleted": {"description": "任务编辑页面中，计时状态提示"}, "taskEditPage_timerActionStatusDropped": "Dropped tasks cannot be timed", "@taskEditPage_timerActionStatusDropped": {"description": "任务编辑页面中，计时状态提示"}, "taskEditPage_timerActionStatusLimitReached": "You have reached the timer limit, please subscribe", "@taskEditPage_timerActionStatusLimitReached": {"description": "任务编辑页面中，计时状态提示"}, "taskEditPage_timerActionStatusReady": "Click to start timing the task", "@taskEditPage_timerActionStatusReady": {"description": "任务编辑页面中，计时状态提示"}, "taskDatePicker_noDate": "No due date", "@taskDatePicker_noDate": {"description": "任务日期选择页面，没有截止日期提示"}, "taskDatePicker_nextMonday": "Next Monday", "@taskDatePicker_nextMonday": {"description": "任务日期选择页面，下周一选项"}, "taskProjectPicker_noProject": "No associated project", "@taskProjectPicker_noProject": {"description": "任务项目选择页面，没有项目提示"}, "taskProjectPicker_emptyMessage": "No projects yet, please create one first", "@taskProjectPicker_emptyMessage": {"description": "任务项目选择页面，需要创建项目提示"}, "taskProjectPicker_createProject": "Create New Project", "@taskProjectPicker_createProject": {"description": "任务项目选择页面，创建项目提示"}, "taskClockPicker_noDueClock": "No due time", "@taskClockPicker_noDueClock": {"description": "任务时钟选择页面，没有截止时间提示"}, "taskClockPicker_title": "Select Due Time", "@taskClockPicker_title": {"description": "任务时钟选择页面，选择截止时间提示"}, "taskEventPicker_noEventClock": "No event scheduled", "@taskEventPicker_noEventClock": {"description": "任务事件选择页面，没有事件安排提示"}, "taskEventPicker_title": "Select Event Time", "@taskEventPicker_title": {"description": "任务事件选择页面，选择时间提示"}, "taskEventPicker_errorNoStartClock": "Please select a start time", "@taskEventPicker_errorNoStartClock": {"description": "任务事件选择页面，选择开始时间提示"}, "taskEventPicker_errorNoEndClock": "Please select an end time", "@taskEventPicker_errorNoEndClock": {"description": "任务事件选择页面，选择结束时间提示"}, "taskEventPicker_errorEndClockBeforeStartClock": "End time cannot be earlier than start time", "@taskEventPicker_errorEndClockBeforeStartClock": {"description": "任务事件选择页面，结束时间早于开始时间提示"}, "taskEventPicker_startClockLabel": "Start Time", "@taskEventPicker_startClockLabel": {"description": "任务事件选择页面，开始时间提示"}, "taskEventPicker_endClockLabel": "End Time", "@taskEventPicker_endClockLabel": {"description": "任务事件选择页面，结束时间提示"}, "taskEventPicker_selectClock": "Select time", "@taskEventPicker_selectClock": {"description": "任务事件选择页面，选择时间提示"}, "taskLongPressSheet_drop": "Drop Task", "@taskLongPressSheet_drop": {"description": "任务长按操作页面，丢弃任务提示"}, "taskLongPressSheet_dropDesc": "The task will become inoperable", "@taskLongPressSheet_dropDesc": {"description": "任务长按操作页面，丢弃任务提示"}, "taskLongPressSheet_restore": "Restore Task", "@taskLongPressSheet_restore": {"description": "任务长按操作页面，恢复任务提示"}, "taskLongPressSheet_restoreDesc": "The task will be restored to an operable state", "@taskLongPressSheet_restoreDesc": {"description": "任务长按操作页面，恢复任务提示"}, "taskLongPressSheet_delete": "Delete Task", "@taskLongPressSheet_delete": {"description": "任务长按操作页面，删除任务提示"}, "taskLongPressSheet_deleteDesc": "Associated timers will also be deleted", "@taskLongPressSheet_deleteDesc": {"description": "任务长按操作页面，删除任务提示"}, "taskLongPressSheet_confirmDeleteTitle": "Confirm Deletion", "@taskLongPressSheet_confirmDeleteTitle": {"description": "任务长按操作页面，确认删除提示"}, "taskLongPressSheet_confirmDeleteContent": "Are you sure you want to permanently delete the task \"{name}\"? This action cannot be undone.", "@taskLongPressSheet_confirmDeleteContent": {"description": "任务长按操作页面，确认删除提示", "placeholders": {"name": {"type": "String"}}}, "timerEditSheet_title": "Edit Timer", "@timerEditSheet_title": {"description": "计时编辑页面中，标题"}, "timerEditSheet_startLabel": "Start:", "@timerEditSheet_startLabel": {"description": "计时编辑页面中，开始时间提示"}, "timerEditSheet_endLabel": "End:", "@timerEditSheet_endLabel": {"description": "计时编辑页面中，结束时间提示"}, "timerEditSheet_deleteTimer": "Delete this timer", "@timerEditSheet_deleteTimer": {"description": "计时编辑页面中，删除计时提示"}, "timerEditSheet_errorEndClockBeforeStartClock": "End time cannot be earlier than start time.", "@timerEditSheet_errorEndClockBeforeStartClock": {"description": "计时编辑页面中，结束时间早于开始时间提示"}, "timerEditSheet_confirmDeleteTitle": "Confirm Deletion", "@timerEditSheet_confirmDeleteTitle": {"description": "计时编辑页面中，确认删除提示"}, "timerEditSheet_confirmDeleteContent": "Are you sure you want to delete this timer record? This action cannot be undone.", "@timerEditSheet_confirmDeleteContent": {"description": "计时编辑页面中，确认删除提示"}, "timerClockSheet_title": "Select Time", "@timerClockSheet_title": {"description": "计时时钟编辑页面标题"}, "projectEditSheet_titleEdit": "Edit Project", "@projectEditSheet_titleEdit": {"description": "项目编辑页面中，编辑项目标题"}, "projectEditSheet_titleCreate": "Create Project", "@projectEditSheet_titleCreate": {"description": "项目编辑页面中，创建项目标题"}, "projectEditSheet_nameHint": "Enter project name...", "@projectEditSheet_nameHint": {"description": "项目编辑页面中，项目名称提示"}, "projectEditSheet_validatorEmpty": "Project name cannot be empty", "@projectEditSheet_validatorEmpty": {"description": "项目编辑页面中，项目名称不能为空提示"}, "projectEditSheet_validatorLength": "Project name cannot exceed {max} characters", "@projectEditSheet_validatorLength": {"description": "项目编辑页面中，项目名称长度超出提示", "placeholders": {"max": {"type": "int"}}}, "projectLongPressSheet_unarchive": "Unarchive", "@projectLongPressSheet_unarchive": {"description": "项目长按操作页面，取消归档提示"}, "projectLongPressSheet_unarchiveDesc": "The project will be moved back to the list page", "@projectLongPressSheet_unarchiveDesc": {"description": "项目长按操作页面，取消归档提示"}, "projectLongPressSheet_archive": "Archive Project", "@projectLongPressSheet_archive": {"description": "项目长按操作页面，归档提示"}, "projectLongPressSheet_archiveDesc": "The project will be moved to the archive page", "@projectLongPressSheet_archiveDesc": {"description": "项目长按操作页面，归档提示"}, "projectLongPressSheet_deleteProject": "Delete Project", "@projectLongPressSheet_deleteProject": {"description": "项目长按操作页面，删除项目提示"}, "projectLongPressSheet_deleteProjectDesc": "Tasks will be kept but no longer associated with this project", "@projectLongPressSheet_deleteProjectDesc": {"description": "项目长按操作页面，删除项目提示"}, "projectLongPressSheet_deleteProjectAndTasks": "Delete Project and Tasks", "@projectLongPressSheet_deleteProjectAndTasks": {"description": "项目长按操作页面，删除项目和任务提示"}, "projectLongPressSheet_deleteProjectAndTasksDesc": "All tasks under the project will be permanently deleted", "@projectLongPressSheet_deleteProjectAndTasksDesc": {"description": "项目长按操作页面，删除项目和任务提示"}, "projectLongPressSheet_confirmDeleteTitle": "Confirm Delete Project?", "@projectLongPressSheet_confirmDeleteTitle": {"description": "项目长按操作页面，确认删除提示"}, "projectLongPressSheet_confirmDeleteContent": "Project \"{name}\" will be deleted, but its tasks will be kept (no longer associated with this project). This action cannot be undone.", "@projectLongPressSheet_confirmDeleteContent": {"description": "项目长按操作页面，确认删除提示", "placeholders": {"name": {"type": "String"}}}, "projectLongPressSheet_deleteFailed": "Failed to delete project: {error}", "@projectLongPressSheet_deleteFailed": {"description": "项目长按操作页面，删除项目失败提示", "placeholders": {"error": {"type": "String"}}}, "projectLongPressSheet_confirmDeleteWithTasksTitle": "Confirm Delete Project and All Tasks?", "@projectLongPressSheet_confirmDeleteWithTasksTitle": {"description": "项目长按操作页面，确认删除提示"}, "projectLongPressSheet_confirmDeleteWithTasksContent": "Project \"{name}\" and all its tasks will be permanently deleted. This action cannot be undone.", "@projectLongPressSheet_confirmDeleteWithTasksContent": {"description": "项目长按操作页面，确认删除提示", "placeholders": {"name": {"type": "String"}}}, "projectLongPressSheet_deleteWithTasksFailed": "Failed to delete project and tasks: {error}", "@projectLongPressSheet_deleteWithTasksFailed": {"description": "项目长按操作页面，删除项目和任务失败提示", "placeholders": {"error": {"type": "String"}}}, "recurringEditPage_titleEdit": "Edit Recurring Task", "@recurringEditPage_titleEdit": {"description": "周期任务编辑页面中，编辑标题"}, "recurringEditPage_titleCreate": "New Recurring Task", "@recurringEditPage_titleCreate": {"description": "周期任务编辑页面中，创建标题"}, "recurringEditPage_creationHint": "Task instances for the next 30 days will be created initially, and then rolled daily.", "@recurringEditPage_creationHint": {"description": "周期任务编辑页面中，创建提示"}, "recurringEditPage_nameHint": "What do you plan to repeat?", "@recurringEditPage_nameHint": {"description": "周期任务编辑页面中，名称提示"}, "recurringEditPage_errorRuleEmpty": "Recurring rule cannot be empty", "@recurringEditPage_errorRuleEmpty": {"description": "周期任务编辑页面中，重复规则不能为空"}, "recurringEditPage_errorSaveFailed": "Failed to create/update recurring task: {error}", "@recurringEditPage_errorSaveFailed": {"description": "周期任务编辑页面中，保存失败提示", "placeholders": {"error": {"type": "String"}}}, "recurringRulesPicker_noRule": "No recurring rule", "@recurringRulesPicker_noRule": {"description": "周期任务规则选择页面中，没有规则提示"}, "recurringRulesPicker_title": "Set Recurrence", "@recurringRulesPicker_title": {"description": "周期任务规则选择页面中，选择规则提示"}, "rulesModePicker_title": "Repeat Mode", "@rulesModePicker_title": {"description": "周期任务规则选择页面中，选择模式提示"}, "rulesDowPicker_title": "Select Weekdays", "@rulesDowPicker_title": {"description": "周期任务规则选择页面中，选择星期提示"}, "rulesDowPicker_placeholder": "Select weekdays", "@rulesDowPicker_placeholder": {"description": "周期任务规则选择页面中，选择星期提示"}, "rulesDowPicker_weekdays": "<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>", "@rulesDowPicker_weekdays": {"description": "周期任务规则选择页面中，选择星期提示"}, "rulesDomPicker_title": "Select Dates", "@rulesDomPicker_title": {"description": "周期任务规则选择页面中，选择日期提示"}, "rulesDomPicker_placeholder": "Select dates", "@rulesDomPicker_placeholder": {"description": "周期任务规则选择页面中，选择日期提示"}, "rulesDoyPicker_title": "Select Dates", "@rulesDoyPicker_title": {"description": "周期任务规则选择页面中，选择日期提示"}, "rulesDoyPicker_placeholder": "Select dates", "@rulesDoyPicker_placeholder": {"description": "周期任务规则选择页面中，选择日期提示"}, "rulesStartPicker_title": "Starts on", "@rulesStartPicker_title": {"description": "周期任务规则选择页面中，开始日期提示"}, "rulesStartPicker_placeholder": "Select start date", "@rulesStartPicker_placeholder": {"description": "周期任务规则选择页面中，开始日期提示"}, "rulesEndPicker_title": "Ends on", "@rulesEndPicker_title": {"description": "周期任务规则选择页面中，结束日期提示"}, "rulesEndPicker_neverEnds": "Never ends", "@rulesEndPicker_neverEnds": {"description": "周期任务规则选择页面中，结束日期提示"}, "rulesCountPicker_title": "Repeat Count", "@rulesCountPicker_title": {"description": "周期任务规则选择页面中，选择次数提示"}, "rulesCountPicker_timesSuffix": "times", "@rulesCountPicker_timesSuffix": {"description": "周期任务规则选择页面中，选择次数提示"}, "rulesCountPicker_unlimited": "Unlimited", "@rulesCountPicker_unlimited": {"description": "周期任务规则选择页面中，选择次数提示"}, "recurringLongPressSheet_delete": "Delete Recurring Task", "@recurringLongPressSheet_delete": {"description": "周期任务长按操作页面，删除提示"}, "recurringLongPressSheet_deleteDesc": "Permanently deletes the recurring task and its instances", "@recurringLongPressSheet_deleteDesc": {"description": "周期任务长按操作页面，删除提示"}, "recurringLongPressSheet_confirmDeleteTitle": "Confirm Deletion", "@recurringLongPressSheet_confirmDeleteTitle": {"description": "周期任务长按操作页面，确认删除提示"}, "recurringLongPressSheet_confirmDeleteContent": "Are you sure you want to permanently delete the recurring task \"{name}\"? This action cannot be undone.", "@recurringLongPressSheet_confirmDeleteContent": {"description": "周期任务长按操作页面，确认删除提示", "placeholders": {"name": {"type": "String"}}}, "changeUsernameSheet_title": "Change Username", "@changeUsernameSheet_title": {"description": "用户名修改页面中，标题"}, "changeUsernameSheet_label": "Username", "@changeUsernameSheet_label": {"description": "用户名修改页面中，用户名提示"}, "changeUsernameSheet_validatorEmpty": "Username cannot be empty", "@changeUsernameSheet_validatorEmpty": {"description": "用户名修改页面中，用户名不能为空提示"}, "changeUsernameSheet_validatorLength": "Username cannot exceed {max} characters", "@changeUsernameSheet_validatorLength": {"description": "用户名修改页面中，用户名长度超出提示", "placeholders": {"max": {"type": "int"}}}, "deleteAccountSheet_title": "Delete Account", "@deleteAccountSheet_title": {"description": "账户删除页面中，标题"}, "deleteAccountSheet_warning": "This action cannot be undone. All your data, including tasks, projects, etc., will be permanently deleted.", "@deleteAccountSheet_warning": {"description": "账户删除页面中，警告提示"}, "deleteAccountSheet_confirmTitle": "Are you sure?", "@deleteAccountSheet_confirmTitle": {"description": "账户删除页面中，确认删除提示"}, "deleteAccountSheet_confirmContent": "Once the account is deleted, it cannot be recovered.", "@deleteAccountSheet_confirmContent": {"description": "账户删除页面中，确认删除提示"}, "deleteAccountSheet_successed": "Your account and all associated data have been permanently removed. Thanks for being with us. Feel free to start a new journey anytime!", "@deleteAccountSheet_successed": {"description": "账户删除页面中，删除成功提示"}, "todayGlanceWidget_todo": "Todo", "@todayGlanceWidget_todo": {"description": "今日概览页面中，待办任务数量"}, "todayGlanceWidget_timer": "Timer", "@todayGlanceWidget_timer": {"description": "今日概览页面中，计时时长"}, "todayGlanceWidget_overdue": "Overdue", "@todayGlanceWidget_overdue": {"description": "今日概览页面中，逾期任务数量"}, "taskPriorityWidget_important": "Important", "@taskPriorityWidget_important": {"description": "任务优先级页面中，重要选项"}, "taskPriorityWidget_urgent": "<PERSON><PERSON>", "@taskPriorityWidget_urgent": {"description": "任务优先级页面中，紧急选项"}, "dateGroupedTasksWidget_empty": "No tasks", "@dateGroupedTasksWidget_empty": {"description": "日期分组任务页面中，没有任务提示"}, "weeklyTaskChart_title": "This Week's Task Completion", "@weeklyTaskChart_title": {"description": "周任务完成统计图表标题"}, "weeklyTaskChart_legendDue": "By Due Date", "@weeklyTaskChart_legendDue": {"description": "周任务完成统计图表中，按截止日期统计的提示"}, "weeklyTaskChart_legendCompleted": "By Completion Date", "@weeklyTaskChart_legendCompleted": {"description": "周任务完成统计图表中，按完成日期统计的提示"}, "weeklyTimerChart_title": "This Week's Timer Duration", "@weeklyTimerChart_title": {"description": "周计时统计图表标题"}, "habitTaskChart_error": "Failed to load habit data", "@habitTaskChart_error": {"description": "习惯统计图表中，加载失败提示"}, "habitTimerChart_note": "* Stats are based on the task's due date, not the timer's date.", "@habitTimerChart_note": {"description": "习惯计时统计图表中，提示文字"}, "navModel_next1DayLabel": "Today", "@navModel_next1DayLabel": {"description": "导航项标签"}, "navModel_next1DayName": "Today Tasks", "@navModel_next1DayName": {"description": "导航项名称"}, "navModel_next1DayDesc": "Show all tasks for today", "@navModel_next1DayDesc": {"description": "导航项描述"}, "navModel_next3DaysLabel": "3 Days", "@navModel_next3DaysLabel": {"description": "导航项标签"}, "navModel_next3DaysName": "3 Days Tasks", "@navModel_next3DaysName": {"description": "导航项名称"}, "navModel_next3DaysDesc": "Show all tasks for the next 3 days", "@navModel_next3DaysDesc": {"description": "导航项描述"}, "navModel_next7DaysLabel": "7 Days", "@navModel_next7DaysLabel": {"description": "导航项标签"}, "navModel_next7DaysName": "7 Days Tasks", "@navModel_next7DaysName": {"description": "导航项名称"}, "navModel_next7DaysDesc": "Show all tasks for the next 7 days", "@navModel_next7DaysDesc": {"description": "导航项描述"}, "navModel_inboxLabel": "Inbox", "@navModel_inboxLabel": {"description": "导航项标签"}, "navModel_inboxName": "Inbox Tasks", "@navModel_inboxName": {"description": "导航项名称"}, "navModel_inboxDesc": "Show all tasks without due time or project", "@navModel_inboxDesc": {"description": "导航项描述"}, "navModel_calendarListLabel": "Calendar", "@navModel_calendarListLabel": {"description": "导航项标签"}, "navModel_calendarListName": "Calendar List", "@navModel_calendarListName": {"description": "导航项名称"}, "navModel_calendarListDesc": "Show calendar and task list", "@navModel_calendarListDesc": {"description": "导航项描述"}, "navModel_monthCalendarLabel": "Calendar", "@navModel_monthCalendarLabel": {"description": "导航项标签"}, "navModel_monthCalendarName": "Month Calendar", "@navModel_monthCalendarName": {"description": "导航项名称"}, "navModel_monthCalendarDesc": "Show monthly calendar tasks", "@navModel_monthCalendarDesc": {"description": "导航项描述"}, "navModel_eisenhowerMatrixLabel": "Matrix", "@navModel_eisenhowerMatrixLabel": {"description": "导航项标签"}, "navModel_eisenhowerMatrixName": "<PERSON>", "@navModel_eisenhowerMatrixName": {"description": "导航项名称"}, "navModel_eisenhowerMatrixDesc": "Show Eisenhower matrix tasks", "@navModel_eisenhowerMatrixDesc": {"description": "导航项描述"}, "navModel_todoLabel": "Todo", "@navModel_todoLabel": {"description": "导航项标签"}, "navModel_todoName": "Todo Tasks", "@navModel_todoName": {"description": "导航项名称"}, "navModel_todoDesc": "Show all unfinished tasks before their deadline", "@navModel_todoDesc": {"description": "导航项描述"}, "navModel_overdueLabel": "Overdue", "@navModel_overdueLabel": {"description": "导航项标签"}, "navModel_overdueName": "Overdue Tasks", "@navModel_overdueName": {"description": "导航项名称"}, "navModel_overdueDesc": "Show all unfinished tasks past their deadline", "@navModel_overdueDesc": {"description": "导航项描述"}, "navModel_completedLabel": "Completed", "@navModel_completedLabel": {"description": "导航项标签"}, "navModel_completedName": "Completed Tasks", "@navModel_completedName": {"description": "导航项名称"}, "navModel_completedDesc": "Show all completed tasks", "@navModel_completedDesc": {"description": "导航项描述"}, "navModel_droppedLabel": "Dropped", "@navModel_droppedLabel": {"description": "导航项标签"}, "navModel_droppedName": "Dropped Tasks", "@navModel_droppedName": {"description": "导航项名称"}, "navModel_droppedDesc": "Show all dropped tasks", "@navModel_droppedDesc": {"description": "导航项描述"}, "navModel_timersLabel": "Timers", "@navModel_timersLabel": {"description": "导航项标签"}, "navModel_timersName": "Timers", "@navModel_timersName": {"description": "导航项名称"}, "navModel_timersDesc": "Show all timers", "@navModel_timersDesc": {"description": "导航项描述"}, "navModel_tasksTimersLabel": "Timers", "@navModel_tasksTimersLabel": {"description": "导航项标签"}, "navModel_tasksTimersName": "Tasks Timers", "@navModel_tasksTimersName": {"description": "导航项名称"}, "navModel_tasksTimersDesc": "Show all tasks timers", "@navModel_tasksTimersDesc": {"description": "导航项描述"}, "navModel_projectsLabel": "Projects", "@navModel_projectsLabel": {"description": "导航项标签"}, "navModel_projectsName": "Projects", "@navModel_projectsName": {"description": "导航项名称"}, "navModel_projectsDesc": "Show all projects", "@navModel_projectsDesc": {"description": "导航项描述"}, "navModel_projectLabel": "Project", "@navModel_projectLabel": {"description": "导航项标签"}, "navModel_projectName": "{projectName} (Project)", "@navModel_projectName": {"description": "导航项名称", "placeholders": {"projectName": {"type": "String"}}}, "navModel_projectDesc": "Show all tasks in this project", "@navModel_projectDesc": {"description": "导航项描述"}, "navModel_recurringsLabel": "Recurrings", "@navModel_recurringsLabel": {"description": "导航项标签"}, "navModel_recurringsName": "Recurring Tasks", "@navModel_recurringsName": {"description": "导航项名称"}, "navModel_recurringsDesc": "Show all recurring tasks", "@navModel_recurringsDesc": {"description": "导航项描述"}, "navModel_statisticsLabel": "Stats", "@navModel_statisticsLabel": {"description": "导航项标签"}, "navModel_statisticsName": "Statistics", "@navModel_statisticsName": {"description": "导航项名称"}, "navModel_statisticsDesc": "Show all statistics", "@navModel_statisticsDesc": {"description": "导航项描述"}, "navModel_entryLabel": "Entry", "@navModel_entryLabel": {"description": "导航项标签"}, "navModel_entryName": "Entry", "@navModel_entryName": {"description": "导航项名称"}, "navModel_entryDesc": "Show all views shortcuts", "@navModel_entryDesc": {"description": "导航项描述"}, "statModel_weeklyTaskName": "Weekly Tasks", "@statModel_weeklyTaskName": {"description": "统计图：周任务统计的名称"}, "statModel_weeklyTaskDescription": "Completion stats for daily tasks this week", "@statModel_weeklyTaskDescription": {"description": "统计图：周任务统计的描述"}, "statModel_weeklyTimerName": "Weekly Timers", "@statModel_weeklyTimerName": {"description": "统计图：周计时统计的名称"}, "statModel_weeklyTimerDescription": "Duration stats for daily timers this week", "@statModel_weeklyTimerDescription": {"description": "统计图：周计时统计的描述"}, "statModel_habitTaskName": "{habitName} (Task)", "@statModel_habitTaskName": {"description": "统计图：习惯任务图表的名称, e.g., 'Reading (Task)'", "placeholders": {"habitName": {"type": "String"}}}, "statModel_habitTaskDescription": "Track completion for habit \"{habitName}\"", "@statModel_habitTaskDescription": {"description": "统计图：习惯任务图表的描述", "placeholders": {"habitName": {"type": "String"}}}, "statModel_habitTimerName": "{habitName} (Timer)", "@statModel_habitTimerName": {"description": "统计图：习惯计时图表的名称, e.g., 'Reading (Timer)'", "placeholders": {"habitName": {"type": "String"}}}, "statModel_habitTimerDescription": "Track timer duration for habit \"{habitName}\"", "@statModel_habitTimerDescription": {"description": "统计图：习惯计时图表的描述", "placeholders": {"habitName": {"type": "String"}}}, "statModel_timeProgressName": "Time Progress", "@statModel_timeProgressName": {"description": "统计图：时间进度的名称"}, "statModel_timeProgressDescription": "Ring progress stats for day, week, month, and year", "@statModel_timeProgressDescription": {"description": "统计图：时间进度的描述"}, "themeModel_forestGreen": "Forest Green", "@themeModel_forestGreen": {"description": "应用主题：森林绿"}, "themeModel_morningMist": "Morning Mist", "@themeModel_morningMist": {"description": "应用主题：晨雾白"}, "recurringModel_every": "Every", "@recurringModel_every": {"description": "周期规则描述的前缀 '每'"}, "recurringModel_doyFormat": "MMM d", "@recurringModel_doyFormat": {"description": "周期规则中，用于年视图的日期格式"}, "recurringModel_description": "{every} {interval} {frequency} {dates}", "@recurringModel_description": {"description": "周期规则的完整描述模板", "placeholders": {"every": {}, "interval": {}, "frequency": {}, "dates": {}}}, "recurringModel_toDate": " to {date}", "@recurringModel_toDate": {"description": "表示时间范围 '至'，例如: 2024-01-01 to 2024-12-31", "placeholders": {"date": {"type": "String"}}}, "recurringModel_toInfinite": " indefinitely", "@recurringModel_toInfinite": {"description": "周期规则卡片上表示无限重复"}, "recurringModel_repeat": " for {count} times", "@recurringModel_repeat": {"description": "周期规则卡片上表示重复次数", "placeholders": {"count": {"type": "int"}}}, "appException_template": "Failed to {how}: {why}", "@appException_template": {"description": "请求失败时，显示错误信息的模板", "placeholders": {"how": {"type": "String"}, "why": {"type": "String"}}}, "appException_how_initializeApp": "initialize app", "@appException_how_initializeApp": {"description": "初始化应用失败时的 how 参数"}, "appException_how_createAccount": "create account", "@appException_how_createAccount": {"description": "创建账户失败时的 how 参数"}, "appException_how_signIn": "sign in", "@appException_how_signIn": {"description": "登录失败时的 how 参数"}, "appException_how_signOut": "sign out", "@appException_how_signOut": {"description": "登出失败时的 how 参数"}, "appException_how_updateUsername": "update username", "@appException_how_updateUsername": {"description": "更新用户名失败时的 how 参数"}, "appException_how_deleteAccount": "delete account", "@appException_how_deleteAccount": {"description": "删除账户失败时的 how 参数"}, "appException_how_createTask": "create task", "@appException_how_createTask": {"description": "创建任务失败时的 how 参数"}, "appException_how_updateTask": "update task", "@appException_how_updateTask": {"description": "更新任务失败时的 how 参数"}, "appException_how_deleteTask": "delete task", "@appException_how_deleteTask": {"description": "删除任务失败时的 how 参数"}, "appException_how_createTimer": "create timer", "@appException_how_createTimer": {"description": "创建计时失败时的 how 参数"}, "appException_how_updateTimer": "update timer", "@appException_how_updateTimer": {"description": "更新计时失败时的 how 参数"}, "appException_how_deleteTimer": "delete timer", "@appException_how_deleteTimer": {"description": "删除计时失败时的 how 参数"}, "appException_how_createProject": "create project", "@appException_how_createProject": {"description": "创建项目失败时的 how 参数"}, "appException_how_updateProject": "update project", "@appException_how_updateProject": {"description": "更新项目失败时的 how 参数"}, "appException_how_deleteProject": "delete project", "@appException_how_deleteProject": {"description": "删除项目失败时的 how 参数"}, "appException_how_createRecurring": "create recurring", "@appException_how_createRecurring": {"description": "创建周期任务失败时的 how 参数"}, "appException_how_updateRecurring": "update recurring", "@appException_how_updateRecurring": {"description": "更新周期任务失败时的 how 参数"}, "appException_how_deleteRecurring": "delete recurring", "@appException_how_deleteRecurring": {"description": "删除周期任务失败时的 how 参数"}, "appException_why_unauthenticated": "user not signed in", "@appException_why_unauthenticated": {"description": "未登录时的 why 参数"}, "appException_why_permissionDenied": "permission denied", "@appException_why_permissionDenied": {"description": "权限不足时的 why 参数"}, "appException_why_serverError": "a server error occurred", "@appException_why_serverError": {"description": "服务器错误时的 why 参数"}, "appException_why_notFound": "resource not found", "@appException_why_notFound": {"description": "未找到资源时的 why 参数"}, "appException_why_alreadyExists": "resource already exists", "@appException_why_alreadyExists": {"description": "资源已存在时的 why 参数"}, "appException_why_invalidArgument": "invalid argument", "@appException_why_invalidArgument": {"description": "无效参数时的 why 参数"}, "appException_why_credentialAlreadyInUse": "credential already in use", "@appException_why_credentialAlreadyInUse": {"description": "凭证已被使用时的 why 参数"}, "appException_why_emailAlreadyInUse": "email already in use", "@appException_why_emailAlreadyInUse": {"description": "邮箱已被使用时的 why 参数"}, "appException_why_accountExistsWithDifferentCredential": "account exists with different credential", "@appException_why_accountExistsWithDifferentCredential": {"description": "账户已存在，但登录凭证不同时的 why 参数"}, "appException_why_userDisabled": "user disabled", "@appException_why_userDisabled": {"description": "用户账户已被禁用时的 why 参数"}, "appException_why_operationNotAllowed": "operation not allowed", "@appException_why_operationNotAllowed": {"description": "操作不被允许时的 why 参数"}, "appException_why_networkRequestFailed": "network request failed", "@appException_why_networkRequestFailed": {"description": "网络请求失败时的 why 参数"}, "appException_why_operationFailed": "operation failed", "@appException_why_operationFailed": {"description": "操作失败时的 why 参数"}, "appException_why_featureLimited": "feature limit reached for free plan", "@appException_why_featureLimited": {"description": "非订阅用户功能限制时的 why 参数"}, "appException_why_unknown": "an unknown error occurred", "@appException_why_unknown": {"description": "未知错误时的 why 参数"}}