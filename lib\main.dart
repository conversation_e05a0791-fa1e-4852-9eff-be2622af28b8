import 'dart:developer' as developer;
import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
// states
import './states/user_state.dart';
import './states/task_state.dart';
import './states/timer_state.dart';
import './states/project_state.dart';
import './states/recurring_state.dart';
import './states/setting_state.dart';
import './states/nav_state.dart';
import './states/stat_state.dart';
import './states/theme_state.dart';
// services
import './services/user_service.dart';
import './services/task_service.dart';
import './services/timer_service.dart';
import './services/project_service.dart';
import './services/recurring_service.dart';
import './services/setting_service.dart';
import './services/permission_service.dart';
// tools
import './secrets.dart';
import 'tools/config.dart';
// screens
import './screens/app_screen.dart';
// others
import './firebase_options.dart';

Future<void> main() async {
  // Flutter 绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 1. 初始化 Firebase (prod)
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // 2. 获取缓存的用户ID
  final prefs = await SharedPreferences.getInstance();
  final String? cachedUserId = prefs.getString(Config.service.appUserIdKey);

  // 3. 初始化 RevenueCat (prod)
  final packageInfo = await PackageInfo.fromPlatform();
  if (packageInfo.packageName == Config.app.packageName) {
    await Purchases.setLogLevel(LogLevel.warn);
    await Purchases.configure(PurchasesConfiguration(secretRevenueCatKey));
  }

  // 创建 Auth, Firestore 和 Functions 实例
  final auth = FirebaseAuth.instance;
  final firestore = FirebaseFirestore.instance;
  final functions = FirebaseFunctions.instanceFor(region: Config.service.firebaseRegion);

  // 启用 Firestore 的持久化
  firestore.settings = const Settings(persistenceEnabled: true);

  // 初始化 Firebase (dev)
  if (kDebugMode) {
    // Android 真机调试和其他平台不一样，需要指定 IP 地址
    final String host = Platform.isAndroid ? '*************' : 'localhost';
    // Web 调试开启 (Platform 在 Web 端会报错)
    // final String host = 'localhost';
    developer.log("✅ Firebase 模拟器 host: $host");
    try {
      await auth.useAuthEmulator(host, 9009);
      firestore.useFirestoreEmulator(host, 8008);
      functions.useFunctionsEmulator(host, 7007);
    } catch (e) {
      developer.log("⛔ Firebase 模拟器启动错误: $e");
    }
  }

  // 设置应用仅支持竖屏
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  runApp(
    MultiProvider(
      providers: [
        // --- 核心状态 ---

        // UserState
        ChangeNotifierProvider(
          lazy: false,
          create: (context) {
            final userState = UserState(auth, UserService(firestore, functions), cachedUserId);
            userState.initialize();
            return userState;
          },
        ),

        // --- 无依赖的状态 ---

        // SettingService
        Provider<SettingService>(
          create: (context) => SettingService(firestore),
        ),

        // PermissionService
        Provider<PermissionService>(
          create: (context) => PermissionService(),
        ),

        // --- 依赖 SettingService 的状态 ---

        // ThemeState
        ChangeNotifierProxyProvider<SettingService, ThemeState>(
          create: (context) => ThemeState(context.read<SettingService>()),
          update: (context, settingService, previous) {
              return previous!;
          },
        ),

        // --- 依赖 UserState 的状态 (最终门卫模式) ---

        // SettingState
        ChangeNotifierProxyProvider<UserState, SettingState>(
          create: (context) => SettingState(SettingService(firestore)),
          update: (context, userState, previous) {
            previous!.listenToDependencyChanges(userState.user);
            return previous;
          },
        ),

        // ProjectState
        ChangeNotifierProxyProvider<UserState, ProjectState>(
          create: (context) => ProjectState(
            ProjectService(firestore, functions),
            context.read<PermissionService>(),
          ),
          update: (context, userState, previous) {
            previous!.listenToDependencyChanges(userState.user);
            return previous;
          },
        ),

        // RecurringState
        ChangeNotifierProxyProvider<UserState, RecurringState>(
          create: (context) => RecurringState(
            RecurringService(firestore, functions),
            context.read<PermissionService>(),
          ),
          update: (context, userState, previous) {
            previous!.listenToDependencyChanges(userState.user);
            return previous;
          },
        ),

        // TimerState
        ChangeNotifierProxyProvider<UserState, TimerNotifier>(
          create: (context) => TimerNotifier(
            TimerService(firestore, functions),
            context.read<PermissionService>(),
          ),
          update: (context, userState, previous) {
            previous!.listenToDependencyChanges(userState.user);
            return previous;
          },
        ),

        // --- 依赖多个 State 的状态 ---

        // TaskState
        ChangeNotifierProxyProvider2<UserState, SettingState, TaskState>(
          create: (context) => TaskState(TaskService(firestore, functions)),
          update: (context, userState, settingState, previous) {
            previous!.listenToDependencyChanges(userState.user, settingState);
            return previous;
          },
        ),

        // NavState
        ChangeNotifierProxyProvider2<ProjectState, SettingState, NavState>(
          create: (context) => NavState(
            context.read<ProjectState>(),
            context.read<SettingState>(),
            SettingService(firestore),
          ),
          update: (context, projectState, settingState, previous) {
            return previous!;
          },
        ),

        // StatState
        ChangeNotifierProxyProvider2<RecurringState, SettingState, StatState>(
          create: (context) => StatState(
            context.read<RecurringState>(),
            context.read<SettingState>(),
            SettingService(firestore),
          ),
          update: (context, recurringState, settingState, previous) {
            return previous!;
          },
        ),
      ],
      child: const AppScreen(),
    ),
  );
}
